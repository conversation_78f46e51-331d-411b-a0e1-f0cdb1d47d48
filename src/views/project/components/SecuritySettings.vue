<template>
  <div class="security-settings">
    <!-- 管理密码 -->
    <el-collapse-item :title="$t('device.managePassword')" name="managePassword">
      <el-card shadow="always" :style="{ marginTop: '10px', marginBottom: '10px' }" class="box-card">
        <el-main>
          <el-form-item :label="$t('device.oldPassword')" label-position="left" label-width="160px" prop="password">
            <el-input type="password" v-model="oldPassword" show-password clearable />
          </el-form-item>
          <el-form-item :label="$t('device.newPassword')" label-position="left" label-width="160px" prop="system.sysPassword">
            <el-input
              type="password"
              :model-value="modelValue.system?.sysPassword"
              @update:model-value="updateField('system.sysPassword', $event)"
              show-password
              clearable
            />
          </el-form-item>
          <el-form-item
            :label="$t('device.confirmPassword')"
            label-position="left"
            label-width="160px"
            prop="system.reSysPassword"
          >
            <el-input
              type="password"
              :model-value="modelValue.system?.reSysPassword"
              @update:model-value="updateField('system.reSysPassword', $event)"
              show-password
              clearable
            />
          </el-form-item>
        </el-main>
      </el-card>
    </el-collapse-item>

    <!-- 安全管理器 -->
    <el-collapse-item
      :title="$t('device.securityManager')"
      v-if="drawerProps?.row?.supports?.network?.supports.includes('brSafe') && modelValue.network?.brSafe"
      name="securityManager"
    >
      <el-card shadow="always" :style="{ marginTop: '10px', marginBottom: '10px' }" class="box-card">
        <el-main>
          <el-form-item :label="$t('device.status')" label-position="left" label-width="160px">
            <el-text>{{ getModeText(deviceStatus?.network?.brSafe?.mode ?? 0) }}</el-text>
          </el-form-item>

          <el-form-item
            :label="$t('device.lockMode')"
            label-position="left"
            v-if="modelValue.network?.brSafe"
            label-width="160px"
          >
            <el-select
              :model-value="modelValue.network.brSafe.mode"
              @update:model-value="updateField('network.brSafe.mode', $event)"
              :placeholder="$t('device.timeLockingPlaceholder')"
              clearable
            >
              <el-option :label="$t('device.unlocking')" :value="0"></el-option>
              <el-option :label="$t('device.timeLocking')" :value="1"></el-option>
              <el-option :label="$t('device.alwaysLock')" :value="2"></el-option>
            </el-select>
          </el-form-item>

          <template v-if="modelValue?.network?.brSafe">
            <el-form-item
              :label="$t('device.timeLocking')"
              v-if="modelValue?.network?.brSafe?.mode === 1"
              label-position="left"
              label-width="160px"
            >
              <el-select
                :model-value="modelValue.network.brSafe.time"
                @update:model-value="updateField('network.brSafe.time', $event)"
                :placeholder="$t('device.timeLockingPlaceholder')"
              >
                <el-option :label="$t('device.oneMinute')" :value="60"></el-option>
                <el-option :label="$t('device.twoMinutes')" :value="120"></el-option>
                <el-option :label="$t('device.fiveMinutes')" :value="300"></el-option>
                <el-option :label="$t('device.tenMinutes')" :value="600"></el-option>
                <el-option :label="$t('device.thirtyMinutes')" :value="1800"></el-option>
                <el-option :label="$t('device.oneHour')" :value="3600"></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-main>
      </el-card>
    </el-collapse-item>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";

interface Props {
  modelValue: any;
  drawerProps?: any;
  deviceStatus?: any;
}

interface Emits {
  (e: "update:modelValue", value: any): void;
  (e: "update:oldPassword", value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const { t } = useI18n();

const oldPassword = ref("");

const updateField = (path: string, value: any) => {
  const keys = path.split(".");
  // 直接修改原对象，避免创建新对象破坏响应式代理
  let current = props.modelValue;
  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]]) {
      current[keys[i]] = {};
    }
    current = current[keys[i]];
  }

  current[keys[keys.length - 1]] = value;
  // 不需要emit，因为直接修改了响应式对象
};

const getModeText = (mode: number) => {
  switch (mode) {
    case 0:
      return t("device.unlocking");
    case 1:
      return t("device.timeLocking");
    case 2:
      return t("device.alwaysLock");
    default:
      return t("device.unlocking");
  }
};

// 监听 oldPassword 变化并向父组件发送
watch(
  () => oldPassword.value,
  newValue => {
    emit("update:oldPassword", newValue);
  }
);
</script>

<style scoped>
.security-settings {
  width: 100%;
}
</style>
