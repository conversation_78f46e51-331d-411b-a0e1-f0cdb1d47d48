<template>
  <el-card>
    <el-form-item :label="$t('device.startIp')" prop="start" label-position="left" label-width="160px">
      <el-input
        :model-value="props.modelValue?.start || ''"
        @input="updateStart"
        :placeholder="$t('device.startIpPlaceholder')"
        clearable
      >
        <template #prepend>
          <span>{{ props.startIpPrefix }}</span>
        </template>
      </el-input>
      <div style="margin-top: 2px; font-size: 13px; color: #909399">
        <el-icon style="margin-right: 2px; font-size: 14px; vertical-align: middle">
          <InfoFilled />
        </el-icon>
        {{ $t("device.startIpTip") }}
      </div>
    </el-form-item>
    <el-form-item :label="$t('device.allocateNumber')" prop="limit" label-position="left" label-width="160px">
      <el-input
        :model-value="props.modelValue?.limit || ''"
        @input="updateLimit"
        :placeholder="$t('device.allocateNumberPlaceholder')"
        clearable
      ></el-input>
      <div style="margin-top: 2px; font-size: 13px; color: #909399">
        <el-icon style="margin-right: 2px; font-size: 14px; vertical-align: middle">
          <InfoFilled />
        </el-icon>
        {{ $t("device.limitTip") }}
      </div>
    </el-form-item>
  </el-card>
</template>

<script setup lang="ts">
import { InfoFilled } from "@element-plus/icons-vue";

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  startIpPrefix: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(["update:modelValue", "startIpChange"]);

const updateStart = (value: string) => {
  const currentValue = props.modelValue || {};
  emit("update:modelValue", { ...currentValue, start: value });
  emit("startIpChange", value);
};

const updateLimit = (value: string) => {
  const currentValue = props.modelValue || {};
  emit("update:modelValue", { ...currentValue, limit: value });
};
</script>
