import { ref } from "vue";
import { drawerProps, loadedTabs } from "@/api/interface/deviceConfigDrawer";

/**
 * 标签页管理
 */
export function useTabManagement() {
  // 激活的标签页
  const activeNames = ref(["first"]);

  // 根据标签页按需加载数据
  const loadTabData = async (tabName: string) => {
    if (loadedTabs.value.has(tabName)) {
      console.log(`标签页 ${tabName} 数据已加载，跳过重复加载`);
      return true;
    }

    try {
      console.log(`开始按需加载标签页 ${tabName} 的数据...`);

      switch (tabName) {
        case "first": // 设备信息
          // 设备信息需要状态数据，但只请求 supports 中存在的字段
          const firstTabFields = ["lan", "wan", "led", "workmode", "internet"];
          const availableFields = [];

          // 检查哪些字段在 supports 中存在
          const supports = drawerProps.value.row.supports;
          if (supports) {
            const allSupportedFields = [
              ...(supports.system?.supports || []),
              ...(supports.network?.supports || []),
              ...(supports.wireless?.supports || [])
            ];

            firstTabFields.forEach(field => {
              if (allSupportedFields.includes(field)) {
                availableFields.push(field);
              }
            });
          }

          if (availableFields.length > 0) {
            console.log(`设备信息标签页需要加载的字段:`, availableFields);
            // 这里应该调用实际的API加载数据
            // await loadDeviceStatus(availableFields);
          }
          break;

        case "second": // 网络设置
          console.log("加载网络设置数据");
          // 这里应该包含网络设置的数据加载逻辑
          break;

        case "third": // 无线设置
          console.log("加载无线设置数据");
          // 这里应该包含无线设置的数据加载逻辑
          break;

        case "fourth": // 安全设置
          console.log("加载安全设置数据");
          // 这里应该包含安全设置的数据加载逻辑
          break;

        case "fifth": // 端口设置
          console.log("加载端口设置数据");
          // 这里应该包含端口设置的数据加载逻辑
          break;

        case "sixth": // 统计信息
          console.log("加载统计信息数据");
          // 这里应该包含统计信息的数据加载逻辑
          break;

        default:
          console.warn(`未知的标签页: ${tabName}`);
          return false;
      }

      // 标记为已加载
      loadedTabs.value.add(tabName);
      console.log(`标签页 ${tabName} 数据加载完成`);
      return true;
    } catch (error) {
      console.error(`加载标签页 ${tabName} 数据失败:`, error);
      return false;
    }
  };

  // 处理标签页变化
  const handleTabChange = async (activeName: string | number) => {
    console.log("标签页变化:", activeName);

    // 转换为字符串
    const tabName = String(activeName);

    // 为激活的标签页加载数据
    if (tabName && !loadedTabs.value.has(tabName)) {
      await loadTabData(tabName);
    }
  };

  // 重置标签页状态
  const resetTabState = () => {
    activeNames.value = ["first"];
    loadedTabs.value.clear();
  };

  // 预加载关键标签页
  const preloadCriticalTabs = async () => {
    // 预加载第一个标签页
    await loadTabData("first");
  };

  return {
    activeNames,
    loadTabData,
    handleTabChange,
    resetTabState,
    preloadCriticalTabs
  };
}
