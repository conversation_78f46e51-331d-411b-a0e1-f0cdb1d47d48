import { useUserStore } from "@/stores/modules/user";
import { deviceStatusEnum, getDeviceConfigJwe, pushDeviceConfigJwe, renameDevice } from "@/api/modules/project";
import { computed, reactive, ref, watch } from "vue";
import { Configuration } from "@/api/interface/configuration";
import { Project } from "@/api/interface/project";
import { removeEmptyValues } from "@/utils";

import { useI18n } from "vue-i18n";
import i18n from "@/languages";
import { ElMessage } from "element-plus";
import { deviceTrafficWeeklyReport } from "@/api/modules/deviceConfigDrawer";
import {
  deviceConfig,
  DeviceConfigData,
  DeviceWeekStatistics,
  drawerProps,
  SpeedDuplex,
  swIsolate,
  swPoe,
  swPort,
  swQos,
  swStorm,
  swVlan
} from "@/api/interface/device/model";
import { getDescription } from "@/api/interface/device/formatter";
import { cloneDeep, merge } from "lodash";

import portActiveIcon from "@/assets/images/port_active_icon.png";
import portDeactiveIcon from "@/assets/images/port_deactive_icon.png";
import portActiveEleIcon from "@/assets/images/port_active_ele_icon.png";
import portDeactiveEleIcon from "@/assets/images/port_deactive_ele_icon.png";

const t = i18n.global.t;

/**
 * 设备配置抽屉 - 智能字段级分段数据请求功能
 *
 * 功能说明：
 * 1. 智能字段分段：自动识别需要分段的字段（支持任意字段扩展）
 * 2. 可配置分段规则：支持动态添加/移除分段字段和分类映射
 * 3. 每个字段单独请求，避免大数据包问题
 * 4. 支持按需加载，根据页面内容只加载必要数据
 * 5. 智能字段分类：自动推断字段属于system/network/wireless
 *
 * 分段请求机制：
 * 1. sequenceNo字段格式：前两位是随机生成的两位数（1-99），用于标识本次请求
 * 2. 后两位表示当前请求的第几段数据，从01开始
 * 3. 设备返回的sequenceNo表示总共需要多少段数据
 *
 * 使用示例：
 * ```typescript
 * // 基本用法 - 加载所有配置数据（智能分段）
 * await loadDeviceConfig();
 * await loadDeviceStatus();
 *
 * // 按需加载特定字段（智能判断是否分段）
 * await loadDeviceConfigFields(['swPort', 'swPoe', 'customField']);
 * await loadDeviceStatusFields(['topology', 'newField']);
 *
 * // 强制指定字段分段
 * await loadDeviceConfigFields(['field1', 'field2'], ['field1']); // field1强制分段
 * await loadDeviceConfigFieldsWithForcedSegmentation(['swPort', 'customField']); // 所有字段强制分段
 *
 * // 动态配置分段字段
 * addSegmentedFields(['customPortField', 'bigDataField']);
 * removeSegmentedFields(['swPort']);
 * setSegmentedFields(['onlyThisField']);
 * addFieldCategoryMappings({customPortField: 'system'});
 *
 * // 按功能模块加载
 * await loadPortRelatedData();      // 端口相关数据
 * await loadVlanData();             // VLAN相关数据
 * await loadTopologyData();         // 拓扑数据
 * await loadSystemManagementData(); // 系统管理数据
 * await loadWanData();              // WAN数据
 *
 * // 根据标签页按需加载
 * await loadDataByTab('port');      // 加载端口标签页数据
 * await loadDataByTab('vlan');      // 加载VLAN标签页数据
 * await loadDataByTab('topology');  // 加载拓扑标签页数据
 *
 * // 调试和监控
 * console.log(getSegmentedFields());     // 查看当前分段字段列表
 * console.log(getFieldCategoryMap());   // 查看字段分类映射
 * await testFieldSegmentedRequest();    // 测试分段请求功能
 * ```
 *
 * 智能分段规则：
 * - 匹配模式：sw*、*port*、*list、topology、device、user、ap等
 * - 字段分类推断：根据字段名自动判断system/network/wireless
 * - 可扩展配置：支持运行时动态添加新的分段字段和分类
 *
 * 优势：
 * - 自动适应任意字段，无需预定义
 * - 减少网络传输压力，避免大数据包
 * - 提高页面响应速度，按需加载
 * - 并行请求处理，提升加载效率
 * - 自动重试机制，提高请求成功率
 * - 详细的错误处理和日志记录
 * - 灵活的配置管理，支持运行时调整
 */

/**
 * 设备数据加载命令类型
 */
export enum DeviceDataCmd {
  CONFIG = 10, // 设备配置
  STATUS = 4 // 设备状态
}

/**
 * 设备数据类型
 */
export enum DeviceDataType {
  CONFIG = "设备配置",
  STATUS = "设备状态"
}

/**
 * 字段级分段数据请求说明：
 *
 * 新的分段请求机制支持字段级别的分开请求和分段：
 * 1. 智能分段判断：自动识别需要分段的字段（sw*、*port*、*list、topology等）
 * 2. 可配置分段字段：支持动态添加/移除分段字段
 * 3. 每个字段都会单独请求，避免大数据包
 * 4. 智能字段分类：自动推断字段属于system/network/wireless
 *
 * sequenceNo字段格式：前两位是随机生成的两位数（10-99），用于标识本次请求；
 * 后两位表示当前请求的第几段数据，从01开始。
 *
 * 分段请求规则：
 * 1. 第一次请求：sequenceNo为4001（40为随机前缀，01为第1段）
 * 2. 收到响应：sequenceNo为4003，表示数据被分成了3段
 * 3. 继续请求：sequenceNo为4002（获取第2段数据）
 * 4. 继续请求：sequenceNo为4003（获取第3段数据）
 * 5. 当请求的sequenceNo与响应的sequenceNo一致时，请求完成
 * 6. 如果响应中没有sequenceNo，则表示数据不需要分段，响应即为完整数据
 * 7. 不同分段请求的sequenceNo前两位必须一致
 *
 * 示例流程：
 * 请求1: sequenceNo=4001 → 响应: sequenceNo=4003, data=[第1段数据]
 * 请求2: sequenceNo=4002 → 响应: sequenceNo=4003, data=[第2段数据]
 * 请求3: sequenceNo=4003 → 响应: sequenceNo=4003, data=[第3段数据] → 完成
 *
 * 数据合并：
 * - 数组类型数据：将各段数据按顺序合并
 * - 非数组类型数据：使用最后一段的完整数据
 *
 * 配置管理：
 * - addSegmentedFields(['customField']) - 添加自定义分段字段
 * - removeSegmentedFields(['swPort']) - 移除分段字段
 * - setSegmentedFields(['field1', 'field2']) - 重置分段字段列表
 * - addFieldCategoryMappings({customField: 'system'}) - 添加字段分类
 *
 * 按需加载支持：
 * - 根据页面当前显示的标签页或组件只加载对应数据
 * - 支持强制指定字段分段模式
 * - 减少不必要的网络请求
 * - 提高页面响应速度
 */

/**
 * 默认需要分段请求的字段列表（可扩展）
 */
const DEFAULT_SEGMENTED_FIELDS = ["swPoe", "swStorm", "swPort", "swVlan"] as const;

/**
 * 可配置的分段字段列表
 */
let SEGMENTED_FIELDS: string[] = [...DEFAULT_SEGMENTED_FIELDS];

/**
 * 字段分类映射（可扩展）
 */
const FIELD_CATEGORY_MAP: Record<string, "system" | "network" | "wireless"> = {
  // 系统相关字段
  swPoe: "system",
  swStorm: "system",
  swPort: "system",
  topology: "system",
  sysReboot: "system",
  sysPassword: "system",
  sysSave: "system",
  led: "system",
  userList: "system",
  apList: "system",
  apGroup: "system",

  // 网络相关字段
  swVlan: "network",
  swLldp: "network",
  swRstp: "network",
  swIsolate: "network",
  wan: "network",
  lan: "network",
  dhcp: "network",
  brAp: "network",
  join: "network",
  brSafe: "network",

  // 无线相关字段
  wifiTime: "wireless",
  radio0: "wireless",
  radio1: "wireless",
  guest: "wireless"
};

/**
 * 添加分段字段
 * @param fields 要添加的字段列表
 */
export const addSegmentedFields = (fields: string[]): void => {
  fields.forEach(field => {
    if (!SEGMENTED_FIELDS.includes(field)) {
      SEGMENTED_FIELDS.push(field);
    }
  });
  console.log(`已添加分段字段: ${fields.join(", ")}，当前分段字段列表:`, SEGMENTED_FIELDS);
};

/**
 * 移除分段字段
 * @param fields 要移除的字段列表
 */
export const removeSegmentedFields = (fields: string[]): void => {
  SEGMENTED_FIELDS = SEGMENTED_FIELDS.filter(field => !fields.includes(field));
  console.log(`已移除分段字段: ${fields.join(", ")}，当前分段字段列表:`, SEGMENTED_FIELDS);
};

/**
 * 设置分段字段列表
 * @param fields 新的分段字段列表
 */
export const setSegmentedFields = (fields: string[]): void => {
  SEGMENTED_FIELDS = [...fields];
  console.log(`已设置分段字段列表:`, SEGMENTED_FIELDS);
};

/**
 * 添加字段分类映射
 * @param mappings 字段分类映射
 */
export const addFieldCategoryMappings = (mappings: Record<string, "system" | "network" | "wireless">): void => {
  Object.assign(FIELD_CATEGORY_MAP, mappings);
  console.log(`已添加字段分类映射:`, mappings);
};

/**
 * 加载设备数据的参数接口
 */
interface LoadDeviceDataParams {
  cmd: DeviceDataCmd;
  deviceId: string;
  userId: string;
  data: DeviceConfigData;
  sequenceNo?: number; // 分段数据序号
}

/**
 * 字段请求结果接口
 */
interface FieldRequestResult {
  field: string;
  category: "system" | "network" | "wireless";
  data: any;
  success: boolean;
  error?: Error;
}

/**
 * 生成随机两位数
 * @returns number 1-99的随机数
 */
/**
 * 生成两位数的随机数（10-99）
 * @returns 两位数的随机数
 */
const generateRandomTwoDigits = (): number => {
  return Math.floor(Math.random() * 90) + 10; // 生成10-99的随机数
};

/**
 * 格式化数字为两位数字符串，如果是一位数则前面补0
 * @param num 要格式化的数字
 * @returns 两位数的字符串
 */
const formatTwoDigits = (num: number): string => {
  return num < 10 ? `0${num}` : `${num}`;
};

/**
 * 处理单个字段的分段请求
 * @param field 字段名
 * @param category 字段分类
 * @param baseParams 基础请求参数
 * @returns Promise<any>
 */

/**
 * 发送单个分段请求的辅助函数
 * @param field 字段名
 * @param category 字段分类
 * @param baseParams 基础请求参数
 * @param randomPrefix 随机前缀
 * @param currentSequence 当前分段序号
 * @returns Promise<any> 响应数据
 */
const makeSegmentRequest = async (
  field: string,
  category: "system" | "network" | "wireless",
  baseParams: Omit<LoadDeviceDataParams, "data" | "sequenceNo">,
  randomPrefix: number,
  currentSequence: number
): Promise<any> => {
  const formattedPrefix = formatTwoDigits(randomPrefix);
  const formattedSequence = formatTwoDigits(currentSequence);
  const sequenceNoStr = `${formattedPrefix}${formattedSequence}`;
  const sequenceNo = parseInt(sequenceNoStr, 10);

  const requestParams: LoadDeviceDataParams = {
    ...baseParams,
    data: { [category]: [field] },
    sequenceNo
  };

  console.log(
    `🚀 正在加载字段 ${field} 第${currentSequence}段，sequenceNo: ${sequenceNo} (前缀:${formattedPrefix}, 段号:${formattedSequence})`
  );
  console.log(`[DEBUG] ➡️ Sending Request for [${field}] segment #${currentSequence} with sequenceNo: ${sequenceNo}`);
  console.log(`📋 请求参数:`, {
    ...requestParams,
    data: `{${category}: ["${field}"]}`
  });

  let retryCount = 0;
  const maxRetries = 3;

  while (retryCount <= maxRetries) {
    try {
      const response = await getDeviceConfigJwe(requestParams);
      if (!response?.data) {
        throw new Error(`获取字段 ${field} 第${currentSequence}段失败: 响应数据为空`);
      }
      return response;
    } catch (error) {
      console.error(`获取字段 ${field} 第${currentSequence}段失败:`, error);
      retryCount++;
      if (retryCount <= maxRetries) {
        console.log(`第${retryCount}次重试获取字段 ${field} 第${currentSequence}段...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
      } else {
        console.error(`获取字段 ${field} 第${currentSequence}段失败，已重试${maxRetries}次`);
        throw error;
      }
    }
  }
  throw new Error(`获取字段 ${field} 第${currentSequence}段失败，超出最大重试次数`);
};

/**
 * 处理单个字段的分段请求
 * @param field 字段名
 * @param category 字段分类
 * @param baseParams 基础请求参数
 * @returns Promise<any>
 */
const handleSingleFieldSegmentedRequest = async (
  field: string,
  category: "system" | "network" | "wireless",
  baseParams: Omit<LoadDeviceDataParams, "data" | "sequenceNo">,
  randomPrefix: number
): Promise<any> => {
  let allData = {
    [category]: {}
  };
  let currentSequence = 1;
  let totalSegments = 1;
  let isComplete = false;

  try {
    console.log(`🚀 开始处理字段 ${field} 的分段请求，随机前缀: ${formatTwoDigits(randomPrefix)} (${randomPrefix})`);
    console.log(`📋 字段 ${field} 分段请求详情: 前缀=${formatTwoDigits(randomPrefix)}, 分类=${category}`);

    // 循环请求分段，直到请求的sequenceNo与响应的sequenceNo一致
    while (!isComplete) {
      console.log(`🔄 字段 ${field} 请求第${currentSequence}段，使用前缀: ${formatTwoDigits(randomPrefix)}`);
      const response = await makeSegmentRequest(field, category, baseParams, randomPrefix, currentSequence);
      const filteredData = filterEmptyData(response.data);

      // 检查 filteredData 是否有效以及是否包含期望的数据
      if (
        filteredData &&
        typeof filteredData === "object" &&
        filteredData[category] &&
        typeof filteredData[category] === "object" &&
        filteredData[category][field] !== undefined
      ) {
        if (!allData[category]) {
          allData[category] = {};
        }
        const currentSegmentData = filteredData[category][field];
        console.log(`📦 字段 ${field} 第${currentSequence}段数据:`, {
          type: Array.isArray(currentSegmentData) ? "array" : typeof currentSegmentData,
          length: Array.isArray(currentSegmentData) ? currentSegmentData.length : "N/A",
          preview: Array.isArray(currentSegmentData) ? `[${currentSegmentData.length} items]` : currentSegmentData
        });

        if (Array.isArray(currentSegmentData)) {
          // 数组类型数据：追加到现有数组
          if (!allData[category][field]) {
            allData[category][field] = [];
            console.log(`🆕 字段 ${field} 初始化空数组`);
          }
          const beforeLength = allData[category][field].length;
          allData[category][field] = [...allData[category][field], ...currentSegmentData];
          console.log(
            `🔗 字段 ${field} 数组合并: ${beforeLength} + ${currentSegmentData.length} = ${allData[category][field].length}`
          );
        } else if (typeof currentSegmentData === "object" && currentSegmentData !== null) {
          // 对象类型数据：深度合并
          if (!allData[category][field] || typeof allData[category][field] !== "object") {
            allData[category][field] = {};
            console.log(`🆕 字段 ${field} 初始化空对象`);
          }
          merge(allData[category][field], currentSegmentData);
          console.log(`🔗 字段 ${field} 对象合并完成`);
        } else {
          // 其他原始类型数据：直接覆盖
          allData[category][field] = currentSegmentData;
          console.log(`📝 字段 ${field} 原始数据覆盖: ${currentSegmentData}`);
        }
      }

      // 调试响应数据结构
      console.log(`🔍 字段 ${field} 第${currentSequence}段响应数据结构:`, {
        hasData: !!response.data,
        dataType: typeof response.data,
        hasSequenceNoInRoot: response && typeof response === "object" && "sequenceNo" in response,
        hasSequenceNoInData: response.data && typeof response.data === "object" && "sequenceNo" in response.data,
        sequenceNoInRoot: response?.sequenceNo,
        sequenceNoInData: response.data?.sequenceNo,
        responseKeys: response && typeof response === "object" ? Object.keys(response) : "N/A",
        dataKeys: response.data && typeof response.data === "object" ? Object.keys(response.data) : "N/A"
      });

      // 检查响应中的sequenceNo（sequenceNo在响应的根级别，不是在data中）
      if (response && typeof response === "object" && "sequenceNo" in response) {
        const responseSequenceNo = response.sequenceNo;
        const responseSequenceNoStr = responseSequenceNo.toString().padStart(4, "0");
        const responsePrefix = parseInt(responseSequenceNoStr.substring(0, 2), 10);
        const responseSegment = parseInt(responseSequenceNoStr.substring(2, 4), 10);

        console.log(
          `字段 ${field} 第${currentSequence}段响应: sequenceNo=${responseSequenceNo} (${responseSequenceNoStr}), 前缀=${responsePrefix}, 总段数=${responseSegment}`
        );

        // 验证前缀是否匹配（前两位必须一致）
        if (responsePrefix !== randomPrefix) {
          throw new Error(
            `字段 ${field} sequenceNo前缀不匹配，期望: ${randomPrefix} (${formatTwoDigits(randomPrefix)})，实际: ${responsePrefix} (${formatTwoDigits(responsePrefix)})`
          );
        }

        // 响应中的后两位表示总分段数
        totalSegments = responseSegment;

        // 构造当前请求的sequenceNo
        const currentRequestSequenceNo = parseInt(`${formatTwoDigits(randomPrefix)}${formatTwoDigits(currentSequence)}`, 10);

        console.log(
          `字段 ${field} 当前请求sequenceNo=${currentRequestSequenceNo}, 响应sequenceNo=${responseSequenceNo}, 总段数=${totalSegments}`
        );

        // 检查是否完成：当前请求的sequenceNo与响应的sequenceNo一致
        if (currentRequestSequenceNo === responseSequenceNo) {
          isComplete = true;
          console.log(
            `字段 ${field} 分段请求完成！请求sequenceNo=${currentRequestSequenceNo}与响应sequenceNo=${responseSequenceNo}一致，共${totalSegments}段数据`
          );
        } else {
          // 继续请求下一个分段
          currentSequence++;
          console.log(`字段 ${field} 继续请求第${currentSequence}段，目标是获取到sequenceNo=${responseSequenceNo}的响应`);

          // 安全检查：防止无限循环
          if (currentSequence > totalSegments) {
            throw new Error(`字段 ${field} 分段请求超出总段数(${totalSegments})，可能存在逻辑错误`);
          }
        }
      } else {
        // 如果响应中没有sequenceNo，说明不是分段数据，直接完成
        isComplete = true;
        console.log(`字段 ${field} 响应中无sequenceNo，视为单段数据`);
      }
    }
  } catch (error) {
    console.error(`处理字段 ${field} 的分段请求失败:`, error);
    throw error;
  }

  // 输出最终合并结果的摘要
  console.log(`✅ 字段 ${field} 数据加载完成，共${totalSegments}段`);
  if (allData[category] && allData[category][field]) {
    const finalData = allData[category][field];
    console.log(`📊 字段 ${field} 最终数据摘要:`, {
      type: Array.isArray(finalData) ? "array" : typeof finalData,
      length: Array.isArray(finalData) ? finalData.length : "N/A",
      preview: Array.isArray(finalData) ? `[${finalData.length} items]` : finalData
    });
  }
  console.log(`🎯 字段 ${field} 完整数据:`, allData);
  return allData;
};

/**
 * 处理普通字段请求（非分段）
 * @param fields 字段列表
 * @param category 字段分类
 * @param baseParams 基础请求参数
 * @returns Promise<any>
 */
const handleNormalFieldsRequest = async (
  fields: string[],
  category: "system" | "network" | "wireless",
  baseParams: Omit<LoadDeviceDataParams, "data">
): Promise<any> => {
  const requestParams: LoadDeviceDataParams = {
    ...baseParams,
    data: { [category]: fields }
  };

  console.log(`正在加载普通字段 ${category}: [${fields.join(", ")}]`);
  console.log(`普通字段请求参数:`, requestParams);

  try {
    const response = await getDeviceConfigJwe(requestParams);

    if (!response?.data) {
      console.warn(`获取普通字段 ${category} 的响应数据为空，返回空对象`);
      return {};
    }

    const filteredData = filterEmptyData(response.data);
    console.log(`普通字段 ${category} 加载完成:`, filteredData);
    return filteredData;
  } catch (error) {
    console.error(`获取普通字段 ${category} 失败:`, error);
    throw error;
  }
};

/**
 * 智能判断字段是否应该分段
 * @param field 字段名
 * @param category 字段分类
 * @returns 是否需要分段
 */
const shouldFieldBeSegmented = (field: string): boolean => {
  // 1. 明确配置的分段字段
  if (SEGMENTED_FIELDS.includes(field)) {
    return true;
  }

  // 2. 根据字段名称模式判断（端口、设备、用户列表等可能需要分段）
  const segmentPatterns = [
    /^sw.+/i, // 交换机相关字段 (swPort, swVlan, swPoe等)
    /port/i, // 端口相关
    /list$/i, // 列表类型字段
    // /topology/i, // 拓扑相关
    /device/i, // 设备相关
    /user/i, // 用户相关
    /ap/i // AP相关
  ];

  return segmentPatterns.some(pattern => pattern.test(field));
};

/**
 * 智能判断字段分类
 * @param field 字段名
 * @param originalCategory 原始分类
 * @returns 推断的分类
 */
const inferFieldCategory = (
  field: string,
  originalCategory: "system" | "network" | "wireless"
): "system" | "network" | "wireless" => {
  // 1. 使用预定义映射
  if (FIELD_CATEGORY_MAP[field]) {
    return FIELD_CATEGORY_MAP[field];
  }

  // 2. 根据字段名称模式推断
  if (/^sw|port|poe|storm|topology|sys|led|user|ap|reboot|password|save/i.test(field)) {
    return "system";
  }

  if (/^wan|lan|dhcp|vlan|lldp|rstp|isolate|br|join|safe|network/i.test(field)) {
    return "network";
  }

  if (/wifi|radio|guest|wireless|ssid|key|channel|txpower/i.test(field)) {
    return "wireless";
  }

  // 3. 使用原始分类
  return originalCategory;
};

/**
 * 分析需要请求的字段并分组
 * @param supports 设备支持的功能
 * @param forceSegmentedFields 强制分段的字段列表（可选）
 * @returns 分组后的字段
 */
const analyzeFieldRequests = (supports: any, forceSegmentedFields?: string[]) => {
  const fieldGroups: {
    segmentedFields: Array<{ field: string; category: "system" | "network" | "wireless" }>;
    normalFields: Record<"system" | "network" | "wireless", string[]>;
  } = {
    segmentedFields: [],
    normalFields: { system: [], network: [], wireless: [] }
  };

  if (!supports) {
    console.warn(`⚠️ supports 为空，无法分析字段请求`);
    return fieldGroups;
  }

  console.log(`🔍 分析字段请求，supports:`, supports);
  console.log(`🔍 强制分段字段:`, forceSegmentedFields);

  const supportTypes = ["wireless", "network", "system"] as const;
  supportTypes.forEach(supportType => {
    if (supports[supportType] && supports[supportType].supports) {
      const supportedFields = supports[supportType].supports as string[];
      console.log(`📋 ${supportType} 支持的字段:`, supportedFields);

      supportedFields.forEach(field => {
        const category = inferFieldCategory(field, supportType);
        const shouldSegment = forceSegmentedFields ? forceSegmentedFields.includes(field) : shouldFieldBeSegmented(field);

        console.log(`🔍 分析字段 ${field}: 分类=${category}, 分段=${shouldSegment}`);

        if (shouldSegment) {
          fieldGroups.segmentedFields.push({ field, category });
          console.log(`✅ 字段 ${field} 被标记为分段字段 (分类: ${category})`);
        } else {
          fieldGroups.normalFields[category].push(field);
          console.log(`📝 字段 ${field} 被标记为普通字段 (分类: ${category})`);
        }
      });
    } else {
      console.log(`⚠️ ${supportType} 没有支持的字段或结构不正确`);
    }
  });

  console.log(`📊 字段分析结果:`, {
    segmentedFields: fieldGroups.segmentedFields.map(item => `${item.field}(${item.category})`),
    normalFields: fieldGroups.normalFields
  });

  return fieldGroups;
};

/**
 * 新的字段级分段数据加载函数
 * @param cmd 命令类型
 * @param target 目标对象
 * @param type 数据类型
 * @param specificFields 指定要加载的字段（按需加载）
 * @param forceSegmentedFields 强制分段的字段列表（可选）
 * @returns Promise<void>
 */
const loadDeviceDataWithFieldSegmentation = async (
  cmd: DeviceDataCmd,
  target: Partial<Configuration.DeviceConfig>,
  type: DeviceDataType,
  specificFields?: string[],
  forceSegmentedFields?: string[]
): Promise<void> => {
  try {
    const baseParams = {
      cmd,
      deviceId: drawerProps.value.row.deviceId,
      userId: useUserStore().userInfo.userId
    };

    const supports = drawerProps.value.row.supports;
    const fieldGroups = analyzeFieldRequests(supports, forceSegmentedFields);

    // 如果指定了特定字段，只加载这些字段
    if (specificFields && specificFields.length > 0) {
      console.log(
        `🔍 过滤前分段字段:`,
        fieldGroups.segmentedFields.map(item => item.field)
      );
      console.log(`🔍 specificFields:`, specificFields);

      fieldGroups.segmentedFields = fieldGroups.segmentedFields.filter(item => specificFields.includes(item.field));

      // 过滤普通字段
      Object.keys(fieldGroups.normalFields).forEach(category => {
        fieldGroups.normalFields[category] = fieldGroups.normalFields[category].filter(field => specificFields.includes(field));
      });

      console.log(
        `🔍 过滤后分段字段:`,
        fieldGroups.segmentedFields.map(item => item.field)
      );
      console.log(`🔍 过滤后普通字段:`, fieldGroups.normalFields);
    } else {
      console.log(`🔍 没有指定特定字段，使用所有字段`);
    }

    console.log(`开始加载${type}数据，字段分组:`, fieldGroups);

    // 添加详细的调试日志
    console.log(`🔍 调试信息:`);
    console.log(`- 分段字段数量: ${fieldGroups.segmentedFields.length}`);
    console.log(
      `- 分段字段列表:`,
      fieldGroups.segmentedFields.map(item => `${item.field}(${item.category})`)
    );
    console.log(`- 普通字段:`, fieldGroups.normalFields);

    if (fieldGroups.segmentedFields.length === 0) {
      console.warn(`⚠️ 警告: 没有分段字段需要请求！`);
    }

    // 为每个分段字段预先生成一个唯一的随机前缀
    const fieldPrefixMap = new Map<string, number>();
    fieldGroups.segmentedFields.forEach(({ field }) => {
      fieldPrefixMap.set(field, generateRandomTwoDigits());
    });
    console.log("🔒 [DEBUG] Pre-generated and locked prefixes for fields:", fieldPrefixMap);

    const results: FieldRequestResult[] = [];

    // 串行处理分段字段请求 - 等待前一个请求完成后再发送下一个
    console.log(`🔄 开始串行处理分段字段请求，共 ${fieldGroups.segmentedFields.length} 个字段`);
    for (const { field, category } of fieldGroups.segmentedFields) {
      console.log(`🚀 开始处理分段字段: ${field} (${category})`);
      try {
        const prefix = fieldPrefixMap.get(field)!; // 使用预先生成的、固定的前缀
        const data = await handleSingleFieldSegmentedRequest(field, category, baseParams, prefix);
        console.log(`✅ 分段字段 ${field} 加载成功`);
        results.push({ field, category, data, success: true });
      } catch (error) {
        console.error(`❌ 分段字段 ${field} 加载失败:`, error);
        results.push({ field, category, data: null, success: false, error: error as Error });
      }
    }

    // 串行处理普通字段请求 - 等待前一个请求完成后再发送下一个
    const normalFieldEntries = Object.entries(fieldGroups.normalFields).filter(([, fields]) => fields.length > 0);
    console.log(`🔄 开始串行处理普通字段请求，共 ${normalFieldEntries.length} 个分类`);
    for (const [category, fields] of normalFieldEntries) {
      console.log(`🚀 开始处理普通字段分类: ${category}, 字段: [${fields.join(", ")}]`);
      try {
        const data = await handleNormalFieldsRequest(fields, category as "system" | "network" | "wireless", baseParams);
        console.log(`✅ 普通字段分类 ${category} 加载成功`);
        results.push({ field: fields.join(","), category: category as any, data, success: true });
      } catch (error) {
        console.error(`❌ 普通字段分类 ${category} 加载失败:`, error);
        results.push({
          field: fields.join(","),
          category: category as any,
          data: null,
          success: false,
          error: error as Error
        });
      }
    }

    // 合并结果到目标对象
    const mergedData = {};
    results.forEach(result => {
      if (result.success && result.data) {
        Object.keys(result.data).forEach(category => {
          if (!mergedData[category]) {
            mergedData[category] = {};
          }
          Object.assign(mergedData[category], result.data[category]);
        });
      }
    });

    // 使用更安全的自定义合并逻辑，替换 lodash.merge
    Object.keys(mergedData).forEach(category => {
      if (!target[category]) {
        target[category] = {};
      }
      Object.keys(mergedData[category]).forEach(field => {
        const sourceValue = mergedData[category][field];
        // 对于数组，直接替换而不是合并，以避免元素级合并问题
        if (Array.isArray(sourceValue)) {
          target[category][field] = sourceValue;
        } else if (typeof sourceValue === "object" && sourceValue !== null && target[category][field]) {
          // 对于对象，可以继续使用 merge 或手动合并
          merge(target[category][field], sourceValue);
        } else {
          // 对于原始类型，直接赋值
          target[category][field] = sourceValue;
        }
      });
    });

    // 特殊处理设备状态的 bootTime
    if (type === DeviceDataType.STATUS) {
      const bootTimeResult = results.find(r => r.data && r.data.bootTime);
      if (bootTimeResult && bootTimeResult.data.bootTime) {
        drawerProps.value.row.bootTime = Number(bootTimeResult.data.bootTime);
      }
    }

    // 统计加载结果
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    console.log(`${type}数据加载完成: ${successCount}/${totalCount} 个字段成功加载`);

    // 如果有失败的字段，抛出错误
    const failedResults = results.filter(r => !r.success);
    if (failedResults.length > 0) {
      const failedFields = failedResults.map(r => r.field).join(", ");
      console.warn(`部分字段加载失败: ${failedFields}`);
    }
  } catch (error) {
    console.error(`${type}数据加载失败:`, error);
    throw error;
  }
};

/**
 * 兼容旧版本的加载设备数据函数
 * @param cmd 命令类型
 * @param target 目标对象
 * @param type 数据类型
 * @param _useSegmentedRequest 是否使用分段请求模式（已废弃，始终使用新的字段级分段）
 * @returns Promise<void>
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const loadDeviceData = async (
  cmd: DeviceDataCmd,
  target: Partial<Configuration.DeviceConfig>,
  type: DeviceDataType,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _useSegmentedRequest?: boolean
): Promise<void> => {
  console.warn("loadDeviceData 已废弃，正在使用新的字段级分段加载方式");
  return loadDeviceDataWithFieldSegmentation(cmd, target, type);
};

/**
 * 加载设备配置 - 使用智能字段级分段加载
 * @param _useSegmentedRequest 兼容参数，已废弃
 * @returns Promise<Configuration.DeviceConfig>
 */
export const loadDeviceConfig = async (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _useSegmentedRequest?: boolean
): Promise<Configuration.DeviceConfig> => {
  console.log("🚀 loadDeviceConfig: 开始使用字段级分段加载设备配置...");
  await loadDeviceDataWithFieldSegmentation(DeviceDataCmd.CONFIG, deviceConfig, DeviceDataType.CONFIG);
  return deviceConfig;
};

/**
 * 加载设备状态 - 使用智能字段级分段加载
 * @param _useSegmentedRequest 兼容参数，已废弃
 * @returns Promise<void>
 */
export const loadDeviceStatus = async (
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _useSegmentedRequest?: boolean
): Promise<void> => {
  console.log("🚀 loadDeviceStatus: 开始使用字段级分段加载设备状态...");
  await loadDeviceDataWithFieldSegmentation(DeviceDataCmd.STATUS, deviceStatus, DeviceDataType.STATUS);
};

// 过滤掉未返回的数据
function filterEmptyData(data) {
  // 使用全局的removeEmptyValues函数处理数据
  const result = removeEmptyValues(data);
  // 如果removeEmptyValues返回undefined，则返回空对象以避免后续访问错误
  return result || {};
}

// deviceStatus 和 deviceConfig 的初始化不应包含 reboot
export const deviceStatus = reactive<Partial<Configuration.DeviceConfig>>(getDefaultDeviceStatus());

// 控制加密状态的变量
export const encryptRadio0 = ref<boolean>(false);
export const encryptRadio1 = ref<boolean>(false);
export const encryptGuest = ref<boolean>(false);

// 添加标志来跟踪是否手动设置了加密方式
export const manualEncryptRadio0 = ref<boolean>(false);
export const manualEncryptRadio1 = ref<boolean>(false);
export const manualEncryptGuest = ref<boolean>(false);

// 存储原始的key值
export const originalKey0 = ref<string>("");
export const originalKey1 = ref<string>("");
export const originalKeyGuest = ref<string>("");

// 计算属性来决定加密状态
export const encryptionRadio0Method = computed({
  get() {
    return encryptRadio0.value;
  },
  set(value: boolean) {
    manualEncryptRadio0.value = true;
    if (value) {
      // 切换到加密，恢复原始key值
      deviceConfig.wireless.radio0.key = originalKey0.value;
    } else {
      // 切换到不加密，保存当前key值并清空
      originalKey0.value = deviceConfig.wireless.radio0.key;
      deviceConfig.wireless.radio0.key = "";
    }
    encryptRadio0.value = value;
  }
});

export const encryptionRadio1Method = computed({
  get() {
    return encryptRadio1.value;
  },
  set(value: boolean) {
    manualEncryptRadio1.value = true;
    if (value) {
      // 切换到加密，恢复原始key值
      deviceConfig.wireless.radio1.key = originalKey1.value;
    } else {
      // 切换到不加密，保存当前key值并清空
      originalKey1.value = deviceConfig.wireless.radio1.key;
      deviceConfig.wireless.radio1.key = "";
    }
    encryptRadio1.value = value;
  }
});

export const encryptionGuestMethod = computed({
  get() {
    return encryptGuest.value;
  },
  set(value: boolean) {
    manualEncryptGuest.value = true;
    if (value) {
      // 切换到加密，恢复原始key值
      deviceConfig.wireless.guest.key = originalKeyGuest.value;
    } else {
      // 切换到不加密，保存当前key值并清空
      originalKeyGuest.value = deviceConfig.wireless.guest.key;
      deviceConfig.wireless.guest.key = "";
    }
    encryptGuest.value = value;
  }
});

// 初始化时，根据 key 的值设置加密状态
watch(
  () => deviceConfig.wireless?.radio0?.key,
  newValue => {
    if (!manualEncryptRadio0.value) {
      encryptRadio0.value = !!newValue;
      if (newValue) {
        originalKey0.value = newValue;
      }
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => deviceConfig.wireless?.radio1?.key,
  newValue => {
    if (!manualEncryptRadio1.value) {
      encryptRadio1.value = !!newValue;
      if (newValue) {
        originalKey1.value = newValue;
      }
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => deviceConfig.wireless?.guest?.key,
  newValue => {
    if (!manualEncryptGuest.value) {
      encryptGuest.value = !!newValue;
      if (newValue) {
        originalKeyGuest.value = newValue;
      }
    }
  },
  { immediate: true, deep: true }
);

export const startIpPrefix = computed(() => {
  // 获取 LAN 的 IP 地址前三部分
  const lanIp = deviceConfig.network?.lan?.ipaddr || "";
  const parts = lanIp.split(".");
  if (parts.length >= 3) {
    return `${parts[0]}.${parts[1]}.${parts[2]}.`;
  }
  return "";
});

// 监听 row 数据的变化，确保在数据更新后处理
watch(
  () => drawerProps.value.row,
  newRow => {
    if (newRow.supports) {
      // 如果 supports 是 JSON 字符串，则进行解析
      if (typeof newRow.supports === "string") {
        newRow.supports = JSON.parse(newRow.supports);
      }
      console.log("解析后的 supports 数据:", JSON.stringify(newRow.supports));
    }
  },
  { immediate: true }
); // `immediate: true` 确保在组件挂载时立即执行

export const handleStartIpChange = (value: string) => {
  // 限制用户输入为数字，并且仅修改最后一部分
  if (!value || typeof value !== "string") {
    deviceConfig.network.dhcp.start = 0;
    return;
  }
  const numValue = parseInt(value.replace(/[^0-9]/g, ""), 10);
  deviceConfig.network.dhcp.start = isNaN(numValue) ? 0 : numValue;
};

const status = computed(() => drawerProps.value.row.status);

export const statusLabel = computed(() => {
  const statusItem = deviceStatusEnum.value.find(item => Number(item.value) === Number(status.value));
  return statusItem ? statusItem.label : "";
});

export const statusTagType = computed(() => {
  const statusItem = deviceStatusEnum.value.find(item => Number(item.value) === Number(status.value));
  return statusItem ? statusItem.tagType : "";
});

// 使用 ref 存储 Date 类型的时间，添加默认值处理
const defaultBeginTime = new Date("1970-01-01T00:00:00");
const defaultEndTime = new Date("1970-01-01T23:59:59");

export const beginTime = ref<Date>(
  deviceConfig.wireless?.wifiTime?.beginTime
    ? new Date(`1970-01-01T${deviceConfig.wireless.wifiTime.beginTime}:00`)
    : defaultBeginTime
);

export const endTime = ref<Date>(
  deviceConfig.wireless?.wifiTime?.endTime ? new Date(`1970-01-01T${deviceConfig.wireless.wifiTime.endTime}:00`) : defaultEndTime
);

// 监听 beginTime 和 endTime 的变化
watch(beginTime, newVal => {
  deviceConfig.wireless.wifiTime.beginTime = newVal.toTimeString().slice(0, 5);
});
watch(endTime, newVal => {
  deviceConfig.wireless.wifiTime.endTime = newVal.toTimeString().slice(0, 5);
});

// 处理时间输入，转换为字符串
export const handleTimeInput = (key: string) => {
  if (!deviceConfig.wireless?.wifiTime) {
    deviceConfig.wireless = {
      ...deviceConfig.wireless,
      wifiTime: {
        week: "",
        beginTime: "",
        endTime: "",
        enabled: 0
      }
    };
  }

  const time = deviceConfig.wireless.wifiTime[key];
  if (time instanceof Date) {
    // 将时间转换为 "HH:mm" 格式的字符串
    deviceConfig.wireless.wifiTime[key] = time.toTimeString().slice(0, 5);
  }
};

export const formatAutoneg = (autoneg: number) => {
  const { t } = useI18n(); // 此方法需要在 Vue 的上下文中调用
  if (autoneg === 0) {
    return t("common.enforcementMode") + "/" + t("common.disconnect");
  } else {
    return t("common.adaptiveMode") + "/" + t("common.disconnect");
  }
};

export const getPortStates = () => {
  const { t } = useI18n();

  return [
    { icon: portActiveIcon, text: `${t("common.connected")} | ${t("common.powerOff")}` },
    { icon: portDeactiveIcon, text: `${t("common.disconnected")} | ${t("common.powerOff")}` },
    { icon: portActiveEleIcon, text: `${t("common.connected")} | ${t("common.powerOn")}` },
    { icon: portDeactiveEleIcon, text: `${t("common.disconnected")} | ${t("common.powerOn")}` }
  ];
};

export let portDialogVisible = ref(false);

export const showPortDialog = () => {
  portDialogVisible.value = true;
  console.log("showPortDialog called:", portDialogVisible.value); // 调试输出
};

export const selectedRows = ref<any[]>([]); // 保存选中的行数据

// 处理选中行的变化
export const handleSelectionChange = (selection: any[]) => {
  console.log("handleSelectionChange called:", selection);
  if (selection.length > 0) {
    console.log("selection:", selection);
    swPort.value = selection[0];
    const selectedPort = selection[0].name;
    // 查找 deviceConfig.system.swPoe 数组中 port 字段与选中项的 port 匹配的对象
    if (deviceConfig.system.swPoe) {
      const matchingPoe = deviceConfig.system.swPoe.find(poe => poe.name === selectedPort);
      if (matchingPoe) {
        swPoe.value = matchingPoe;
        console.log("swPoe:", JSON.stringify(swPoe.value));
      }
    }
    if (deviceConfig.network.swVlan) {
      const matchingVlan = deviceConfig.network.swVlan.find(vlan => vlan.name === selectedPort);
      if (matchingVlan) {
        swVlan.value = matchingVlan;
        console.log("swVlan:", swVlan.value);
      }
    }
    if (deviceConfig.system.swStorm) {
      const matchingStorm = deviceConfig.system.swStorm.find(storm => storm.name === selectedPort);
      if (matchingStorm) {
        swStorm.value = matchingStorm;
        console.log("swStorm:", JSON.stringify(swStorm.value));
      }
    }
    if (deviceConfig.network.swIsolate) {
      const matchingSwIsolate = deviceConfig.network.swIsolate.find(vlan => vlan.name === selectedPort);
      if (matchingSwIsolate) {
        swIsolate.value = matchingSwIsolate;
        console.log("swIsolate:", JSON.stringify(swIsolate.value));
      }
    }
  }
  selectedRows.value = selection;
};

export const showPortExample = ref(false);
// 切换图示显示状态
export const togglePortExample = () => {
  console.log("selectedRows:{}", selectedRows);
  showPortExample.value = !showPortExample.value;
};

// 计算属性提取 name 字段并用逗号连接
export const selectedRowNames = computed(() => {
  return selectedRows.value.map(row => row.describe || row.name).join(", ");
});

// 判断行是否被选中
export const isRowSelected = row => {
  return selectedRows.value.some(selected => selected.name === row.name);
};

export const isPortSelected = row => {
  if (isolateRows.value.length === 0 && swIsolate.value && swIsolate.value.isolate && swIsolate.value.isolate.length > 0) {
    console.log("swIsolate.value:", swIsolate.value);
    return swIsolate.value.isolate.includes(row.port);
  }
  return isolateRows.value.some(selected => selected.name === row.name);
};

export const portTableRef = ref(null);

export const toggleRowSelection = item => {
  console.log("toggleRowSelection called:", item);
  const index = selectedRows.value.findIndex(row => row.name === item.name);
  if (index === -1) {
    // 未选中，则添加到选中数组
    selectedRows.value.push(item);
  } else {
    // 已选中，则从选中数组中移除
    selectedRows.value.splice(index, 1);
  }
  // 手动更新 el-table 的选中项
  if (portTableRef.value) {
    portTableRef.value.toggleRowSelection(item); // 手动切换表格的选中状态
  }
};

// 监听关闭事件
export const onDrawerClose = () => {
  selectedRows.value = []; // 清空选中项
  deviceNameChanged.value = false; // 重置变量
  editName.value = false;
  activeName.value = "first";
};

// 将枚举转换为选项数组
export const speedDuplexOptions = Object.entries(SpeedDuplex)
  .filter(([, value]) => !isNaN(Number(value))) // 只保留数值类型的项
  .map(([, value]) => ({
    label: Number(value), // 使用国际化函数生成 label
    value: Number(value) // 枚举值作为选项值
  }));

export enum PoePower {
  AF = 0, // af (15.4w)
  AT = 1 // at (30w)
}

// 构建选项列表
export const poePowerOptions = {
  [PoePower.AF]: "af (15.4w)",
  [PoePower.AT]: "at (30w)"
};

export enum VlanMode {
  ACCESS = 0, // access
  TRUNK = 1, // trunk
  HYBRID = 2 // hybrid
}

// 构建 VLAN 模式选项
export const vlanModeOptions = {
  [VlanMode.ACCESS]: "access",
  [VlanMode.TRUNK]: "trunk",
  [VlanMode.HYBRID]: "hybrid"
};

export enum trafficType {
  UNICAST = 4, // unicast
  MULTICAST = 1, // multicast
  BROADCAST = 2 // broadcast
}

// 枚举转为数组
export const trafficTypeOptionsArray = Object.keys(trafficType)
  .filter(key => isNaN(Number(key))) // 过滤掉数字的部分
  .map(key => ({
    label: key, // 选项的显示文本
    value: trafficType[key as keyof typeof trafficType], // 对应的值
    description: getDescription(key) // 添加中文描述
  }));

// 为每个枚举值定义中文描述

// 更新 trafficType 的逻辑
export const selectedTrafficTypes = computed({
  get: () => {
    console.log("Current trafficType:", swStorm.value);
    // 通过位运算解析当前选中的类型
    return trafficTypeOptionsArray.filter(option => (swStorm.value.trafficType & option.value) !== 0).map(option => option.value);
  },
  set: newValues => {
    let updatedValue = 0;
    console.log("New selected values:", newValues);
    // 合并选中的值
    newValues.forEach(value => {
      updatedValue |= value;
      console.log("Current updatedValue:", updatedValue);
    });

    console.log("Before setting trafficType:", swStorm.value.trafficType);
    swStorm.value.trafficType = updatedValue;
    console.log("After setting trafficType:", swStorm.value.trafficType);
  }
});

// 监控 selectedTrafficTypes 的变化
watch(
  () => swStorm.value.trafficType,
  newVal => {
    console.log("swPoe trafficType changed:", newVal);
  }
);

// 当复选框变化时，更新 trafficType 的值
export const updateTrafficType = () => {
  console.log("Updated trafficType:", swStorm.value.trafficType);
};

// 预加载常用值函数
export const preloadRateOptions = async () => {
  const commonOptions = {
    label: t("device.commonRate"),
    value: "common",
    children: [
      { label: "64", value: 64, isLeaf: true },
      { label: "128", value: 128, isLeaf: true },
      { label: "256", value: 256, isLeaf: true },
      { label: "512", value: 512, isLeaf: true },
      { label: "1024", value: 1024, isLeaf: true },
      { label: "2048", value: 2048, isLeaf: true },
      { label: "4096", value: 4096, isLeaf: true },
      { label: "8192", value: 8192, isLeaf: true },
      { label: "16384", value: 16384, isLeaf: true },
      { label: "32768", value: 32768, isLeaf: true }
    ],
    isLeaf: false,
    disabled: true,
    hasChildren: true
  };

  const customOptions = {
    label: t("device.more"),
    value: "custom",
    children: null,
    isLeaf: false,
    loading: false,
    disabled: false,
    expanded: false,
    loaded: false,
    hasChildren: true,
    lazy: true
  };

  return [commonOptions, customOptions];
};

export let rateOptions = ref<Array<any>>([]);

export const isolateRows = ref<any[]>([]); // 保存选中的行数据
export const togglePortSelection = item => {
  console.log("togglePortSelection called:", item);
  const index = isolateRows.value.findIndex(row => row.name === item.name);
  if (index === -1) {
    // 未选中，则添加到选中数组
    isolateRows.value.push(item);
  } else {
    // 已选中，则从选中数组中移除
    isolateRows.value.splice(index, 1);
  }
  console.log("isolateRows updated:", isolateRows.value);
};

export const isolateAll = () => {
  // 获取所有端口项
  const allPorts = deviceConfig.system.swPort;

  if (isolateRows.value.length === allPorts.length) {
    // 如果所有端口已经被选中，则清空选中的端口
    isolateRows.value = [];
  } else {
    // 否则，选中所有端口
    isolateRows.value = [...allPorts];
  }

  console.log("isolateRows updated:", isolateRows.value);
};

export const getPortNames = ports => {
  if (!ports?.isolate) {
    return []; // 如果 ports 或 ports.isolate 不存在，返回空数组
  }
  // console.log("getPortNames called:", ports.isolate);
  return ports?.isolate
    .map(portId => {
      const port = deviceConfig.system.swPort.find(p => p.port === portId);
      return port ? port.name : ""; // 返回 name 字段
    })
    .filter(name => name !== ""); // 过滤掉空字符串
};

export const editName = ref(false);

export const deviceNameChanged = ref(false);

export const editDeviceName = () => {
  editName.value = !editName.value;
};

export const saveDeviceName = async (ruleFormRef?: any) => {
  if (!deviceConfig.deviceName) {
    return;
  }

  // 表单验证，只有验证通过才提交
  if (ruleFormRef) {
    try {
      await ruleFormRef.validateField("deviceName");
    } catch (error) {
      // 验证不通过，保持编辑状态，不提交
      return;
    }
  }

  editName.value = !editName.value;
  drawerProps.value.row.deviceName = deviceConfig.deviceName;
  const params = {
    deviceName: deviceConfig.deviceName,
    deviceId: drawerProps.value.row.deviceId
  };
  try {
    const response = await renameDevice(params);
    if (response && response.code === "200") {
      ElMessage({
        message: response.msg || t("common.operationSuccess"),
        type: "success",
        duration: 3000
      });
    } else {
      ElMessage({
        message: response?.msg,
        type: "error",
        duration: 3000
      });
    }
  } catch (error) {
    ElMessage({
      message: "设备名称更新时发生异常，请检查网络或稍后重试！",
      type: "error",
      duration: 3000
    });
  }
};

export const handleReboot = async () => {
  const t = i18n.global.t;
  try {
    const params: Project.ReqConfigParams = {
      cmd: 6,
      deviceId: drawerProps.value.row.deviceId,
      userId: useUserStore().userInfo.userId,
      data: {
        system: {
          sysReboot: 1
        }
      }
    };
    const response = await pushDeviceConfigJwe(params);
    if (response && response.code === "200" && response.msg === "success") {
      console.log("Device rebooted successfully, response:", response);
      // 请求成功的提示消息
      ElMessage({
        message: t("common.operationSuccess"), // 使用 response.msg
        type: "success", // 成功类型
        duration: 3000
      });
    } else {
      // 请求失败的提示
    }
  } catch (error) {
    console.error("Failed to reboot device:", error);
    ElMessage.error({ message: t("common.requestTimeout"), duration: 3000 });
  }
};

export const progressShow = ref(false); // 控制进度条的显示状态
export const downloadingPer = ref(0); // 下载进度百分比
// 通用的消息提示函数
const showMessage = (message: string, type: "success" | "error") => {
  ElMessage({
    message,
    type,
    duration: 3000
  });
};

// 计算下载进度并显示
const handleDownloadProgress = (otaData: any, t: Function) => {
  const totalSize = otaData?.size || 1; // 避免除以 0
  const downloadedSize = otaData?.dlSize || 0;
  const per = Math.min(Math.round((downloadedSize / totalSize) * 100), 100);
  progressShow.value = true; // 显示进度条
  downloadingPer.value = per; // 更新进度百分比
  showMessage(t("common.downloadingUpdate", { percent: per }), "success");
};

// 处理升级状态
const handleUpgradeState = async (otaData: any, t: Function, fetchUpgradeStatus: Function) => {
  const upgradeState = otaData?.status;

  switch (upgradeState) {
    case "NONE":
      showMessage(t("common.discoverNewVer"), "success"); // 有新版本
      break;
    case "FAIL":
      showMessage(t("common.upgradeFailed"), "error"); // 更新失败
      break;
    case "DL":
      handleDownloadProgress(otaData, t);
      // 每 5 秒重新获取状态
      setTimeout(fetchUpgradeStatus, 5000);
      break;
    case "UPG":
      showMessage(t("common.upgrading"), "success"); // 正在升级
      break;
  }
};

// 获取设备升级状态
const fetchUpgradeStatus = async (deviceId: string, userId: string, t: Function) => {
  try {
    const configResponse = await getDeviceConfigJwe({
      cmd: 4,
      deviceId,
      userId,
      data: { system: ["ota"] }
    });

    if (configResponse?.code === "200" && configResponse.msg === "success") {
      const otaData = configResponse.data?.system?.ota;
      const upgradeStatus = otaData?.upgrade;

      if (upgradeStatus === 1) {
        await handleUpgradeState(otaData, t, () => fetchUpgradeStatus(deviceId, userId, t));
      } else {
        showMessage(t("common.noUpdates"), "success"); // 没有更新
      }
    } else {
      showMessage(configResponse?.msg || t("common.fetchingStatusError"), "error");
    }
  } catch (error) {
    console.error("Failed to fetch upgrade status:", error);
    showMessage(t("common.fetchingStatusError"), "error");
  }
};

// 主函数：触发升级
export const handleUpgrade = async () => {
  const t = i18n.global.t;
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  try {
    // 发送升级请求
    const upgradeResponse = await pushDeviceConfigJwe({
      cmd: 6,
      deviceId,
      userId,
      data: { system: { ota: { check: 1 } } }
    });

    if (upgradeResponse?.code === "200" && upgradeResponse.msg === "success") {
      console.log("Device upgrade check initiated successfully:", upgradeResponse);

      // 开始检查设备升级状态
      await fetchUpgradeStatus(deviceId, userId, t);
    } else {
      showMessage(upgradeResponse?.msg || t("common.upgradeFailed"), "error");
    }
  } catch (error) {
    console.error("Failed to upgrade device:", error);
    showMessage(t("common.upgradeFailed"), "error");
  }
};

export const deviceWeekStatistic = ref<
  | DeviceWeekStatistics
  | {
      deviceId: string;
      deviceName: string;
      beginDate: string;
      beginDateTimeZone?: unknown;
      endDate: string;
      endDateTimeZone?: unknown;
      bootTime: number;
      deviceType: string;
      deviceModel: string;
      rxByte: number;
      txByte: number;
      statistics: { date: string; rxByte: number; txByte: number; hour?: null }[];
    }
  | null
>(null);

export const getDeviceStatistics = async (params: { deviceId: string }) => {
  try {
    const response = await deviceTrafficWeeklyReport(params);
    if (response && response.code === "200" && response.msg === "success") {
      console.log("Device statistics retrieved successfully:", response);
      if (response.data) {
        deviceWeekStatistic.value = response.data || {}; // 更新统计数据
      }
    } else {
      deviceWeekStatistic.value = null; // 清除统计数据或设置为默认值
      console.error("Failed to retrieve device statistics:", response);
      throw new Error(response?.msg || "Failed to fetch device statistics");
    }
  } catch (error) {
    console.error("Error in getDeviceStatistics:", error);
    throw error;
  }
};
export const drawerVisible = ref(false);
export const loadedTabs = ref(new Set<string>()); // 标记已加载的标签页数据
export const clearLoadedTabs = () => {
  loadedTabs.value.clear();
};

// 密码和原始数据
export const password = ref("");
export const originalData = ref<any>(null);

// 获取配置差异
export const getDiff = (original: any, current: any) => {
  if (!original || !current) return current;
  return current; // 简化实现，返回当前配置
};

// 提交配置
export const submitConfig = async (configData: any): Promise<boolean> => {
  try {
    const userStore = useUserStore();

    // 提交前移除 reSysPassword 字段
    if (configData.system && configData.system.reSysPassword !== undefined) {
      delete configData.system.reSysPassword;
    }

    // 组装参数，password 作为旧密码放最外层
    const params: any = {
      deviceId: drawerProps.value.row.deviceId,
      userId: userStore.userInfo.userId,
      data: configData
    };

    // 如果有密码变更，password 需要提交
    if (password.value) {
      params.password = password.value;
    }

    await pushDeviceConfigJwe(params);
    ElMessage.success(t("common.updateSuccess"));

    // 更新原始数据
    originalData.value = cloneDeep(deviceConfig);

    return true;
  } catch (error) {
    console.error("提交配置失败:", error);
    ElMessage.error(t("common.updateFailed"));
    return false;
  }
};
export const activeName = ref<"first" | "second" | "third" | "fourth" | "fifth">("first"); // 底层tabs默认选中第一个
export const dialogTabActive = ref("first");

// 根据选中的端口名称和端口号生成端口提交数据对象
export const generatePortData = (deviceConfig: any): any[] => {
  console.log("generatePortData called with deviceConfig:", deviceConfig);
  const portConfigs: any[] = [];
  selectedRows.value.forEach(port => {
    const config = cloneDeep(deviceConfig); // 使用深拷贝
    config.name = port.name;
    config.port = port.port;
    portConfigs.push(config);
  });
  return portConfigs;
};

export const closeDialog = () => {
  drawerVisible.value = false;
  activeName.value = "first";
};

/**
 * 按需加载特定字段的设备配置
 * @param fields 要加载的字段列表
 * @param forceSegmentedFields 强制分段的字段列表（可选）
 * @returns Promise<Configuration.DeviceConfig>
 */
export const loadDeviceConfigFields = async (
  fields: string[],
  forceSegmentedFields?: string[]
): Promise<Configuration.DeviceConfig> => {
  await loadDeviceDataWithFieldSegmentation(
    DeviceDataCmd.CONFIG,
    deviceConfig,
    DeviceDataType.CONFIG,
    fields,
    forceSegmentedFields
  );
  return deviceConfig;
};

/**
 * 按需加载特定字段的设备状态
 * @param fields 要加载的字段列表
 * @param forceSegmentedFields 强制分段的字段列表（可选）
 * @returns Promise<void>
 */
export const loadDeviceStatusFields = async (fields: string[], forceSegmentedFields?: string[]): Promise<void> => {
  await loadDeviceDataWithFieldSegmentation(
    DeviceDataCmd.STATUS,
    deviceStatus,
    DeviceDataType.STATUS,
    fields,
    forceSegmentedFields
  );
};

/**
 * 加载指定字段并强制使用分段请求
 * @param fields 要加载的字段列表
 * @returns Promise<Configuration.DeviceConfig>
 */
export const loadDeviceConfigFieldsWithForcedSegmentation = async (fields: string[]): Promise<Configuration.DeviceConfig> => {
  return await loadDeviceConfigFields(fields, fields); // 所有字段都强制分段
};

/**
 * 加载指定字段并强制使用分段请求
 * @param fields 要加载的字段列表
 * @returns Promise<void>
 */
export const loadDeviceStatusFieldsWithForcedSegmentation = async (fields: string[]): Promise<void> => {
  return await loadDeviceStatusFields(fields, fields); // 所有字段都强制分段
};

/**
 * 加载端口相关数据（swPort, swPoe, swStorm, swVlan）
 * @returns Promise<void>
 */
export const loadPortRelatedData = async (): Promise<void> => {
  const portFields = ["swPort", "swPoe", "swStorm", "swVlan"];
  await loadDeviceConfigFields(portFields);
};

/**
 * 加载网络拓扑数据
 * @returns Promise<void>
 */
export const loadTopologyData = async (): Promise<void> => {
  await loadDeviceConfigFields(["topology"]);
};

/**
 * 加载VLAN相关数据
 * @returns Promise<void>
 */
export const loadVlanData = async (): Promise<void> => {
  const vlanFields = ["swVlan", "swLldp", "swRstp"];
  await loadDeviceConfigFields(vlanFields);
};

/**
 * 加载系统管理相关数据
 * @returns Promise<void>
 */
export const loadSystemManagementData = async (): Promise<void> => {
  // sysReboot, sysPassword, sysSave 是功能性字段，不需要获取状态，避免 401 错误
  // const systemFields = ["sysReboot", "sysPassword", "sysSave"];
  // await loadDeviceConfigFields(systemFields);
};

/**
 * 加载WAN相关数据
 * @returns Promise<void>
 */
export const loadWanData = async (): Promise<void> => {
  await loadDeviceConfigFields(["wan"]);
};

/**
 * 根据当前活动标签页加载对应数据
 * @param tabName 标签页名称
 * @returns Promise<void>
 */
export const loadDataByTab = async (tabName: string): Promise<void> => {
  const tabFieldMapping: Record<string, string[]> = {
    port: ["swPort", "swPoe", "swStorm"],
    vlan: ["swVlan", "swLldp", "swRstp"],
    topology: ["topology"],
    system: [],
    network: ["wan", "lan", "dhcp", "brAp", "join", "brSafe", "wifiTime", "radio0", "radio1", "guest", "swVlan"],
    wireless: ["wifiTime", "radio0", "radio1", "guest"]
  };

  const fields = tabFieldMapping[tabName] || [];
  if (fields.length > 0) {
    console.log(`根据标签页 ${tabName} 加载字段:`, fields);
    await loadDeviceConfigFields(fields);
  } else {
    console.warn(`未找到标签页 ${tabName} 对应的字段配置`);
  }
};

/**
 * 兼容函数：强制使用新的字段级分段请求模式加载设备配置
 * @deprecated 请使用 loadDeviceConfig() 或 loadDeviceConfigFields()
 * @returns Promise<Configuration.DeviceConfig>
 */
export const loadDeviceConfigWithSegmentedRequest = async (): Promise<Configuration.DeviceConfig> => {
  console.warn("loadDeviceConfigWithSegmentedRequest 已废弃，请使用 loadDeviceConfig() 或 loadDeviceConfigFields()");
  return await loadDeviceConfig();
};

/**
 * 兼容函数：强制使用新的字段级分段请求模式加载设备状态
 * @deprecated 请使用 loadDeviceStatus() 或 loadDeviceStatusFields()
 * @returns Promise<void>
 */
export const loadDeviceStatusWithSegmentedRequest = async (): Promise<void> => {
  console.warn("loadDeviceStatusWithSegmentedRequest 已废弃，请使用 loadDeviceStatus() 或 loadDeviceStatusFields()");
  return await loadDeviceStatus();
};

/**
 * 兼容函数：强制使用新的字段级分段请求模式加载设备配置
 * @deprecated 请使用 loadDeviceConfig() 或 loadDeviceConfigFields()
 * @returns Promise<Configuration.DeviceConfig>
 */
export const loadDeviceConfigWithoutSegmentedRequest = async (): Promise<Configuration.DeviceConfig> => {
  console.warn("loadDeviceConfigWithoutSegmentedRequest 已废弃，请使用 loadDeviceConfig() 或 loadDeviceConfigFields()");
  return await loadDeviceConfig();
};

/**
 * 兼容函数：强制使用新的字段级分段请求模式加载设备状态
 * @deprecated 请使用 loadDeviceStatus() 或 loadDeviceStatusFields()
 * @returns Promise<void>
 */
export const loadDeviceStatusWithoutSegmentedRequest = async (): Promise<void> => {
  console.warn("loadDeviceStatusWithoutSegmentedRequest 已废弃，请使用 loadDeviceStatus() 或 loadDeviceStatusFields()");
  return await loadDeviceStatus();
};

export { deviceConfig, deviceTrafficWeeklyReport, drawerProps, swIsolate, swPoe, swPort, swVlan, swQos, swStorm };

// 获取 deviceConfig 的初始结构
export function getDefaultDeviceConfig(supports?: any) {
  const config: any = {
    network: {
      lan: { netmask: "", ipaddr: "" },
      dhcp: { dnsenabled: 0, dns1: "", dns2: "", start: 0, limit: 0 },
      wan: [],
      wanMax: 0,
      workmode: "",
      brAp: { ssid: "", key: "", hidden: 0, mode: 0, channel: 0, txpower: 0, chanList: [] },
      join: { ssid: "", key: "", radio: 0 },
      brSafe: { mode: 0, status: "", timedown: 0, time: 0 },
      swRstp: [],
      swLldp: [],
      swVlan: [],
      swIsolate: []
    },
    system: {
      // 确保所有 system 相关的属性都被初始化，即使 supports 中没有明确列出
      led: { mode: "", beginTime: "", endTime: "" },
      userList: [],
      sysPassword: "",
      reSysPassword: "",
      swPort: [],
      swPoe: [],
      swStorm: [],
      swQos: [],
      swVlan: [],
      sysSave: 0,
      reboot: { week: "", rateDelay: 0, time: "", enabled: 0 } // 始终初始化 reboot
    },
    wireless: {
      // 始终初始化 wireless
      wifiTime: { enabled: 0, week: "", beginTime: "", endTime: "" },
      radio0: { hidden: 0, chanList: [], txpower: 0, channel: 0, disabled: 0, ssid: "", key: "" },
      radio1: { hidden: 0, chanList: [], txpower: 0, channel: 0, disabled: 0, ssid: "", key: "" },
      guest: { rate: 0, wifiTime: 0, disabled: 0, ssid: "", key: "", hidden: 0 }
    }
  };
  // 只有支持WAN时才初始化 wan 数组
  if (supports?.network?.supports?.includes("wan")) {
    config.network.wan = [
      {
        proto: "dhcp",
        username: "",
        password: "",
        ipaddr: "",
        netmask: "",
        gawa: "",
        dns1: "",
        dns2: ""
      }
    ];
  }
  return config;
}

// 获取 deviceStatus 的初始结构
export function getDefaultDeviceStatus(supports?: any) {
  const status: any = {
    wireless: {
      wifiTime: { enabled: 0, week: "", beginTime: "", endTime: "" },
      radio0: { hidden: 0, chanList: [], txpower: 0, channel: 0, disabled: 0, ssid: "", key: "" },
      radio1: { hidden: 0, chanList: [], txpower: 0, channel: 0, disabled: 0, ssid: "", key: "" },
      guest: { rate: 0, wifiTime: 0, disabled: 0, ssid: "", key: "", hidden: 0 }
    },
    network: {
      lan: { netmask: "", ipaddr: "" },
      dhcp: { dnsenabled: 0, dns1: "", dns2: "", start: 0, limit: 0 },
      wan: [],
      wanMax: 0,
      workmode: "",
      brAp: { ssid: "", key: "", hidden: 0, mode: 0, channel: 0, txpower: 0, chanList: [] },
      join: { ssid: "", key: "", radio: 0 },
      brSafe: { mode: 0, status: "", timedown: 0, time: 0 },
      swRstp: [],
      swLldp: [],
      swVlan: [],
      swIsolate: []
    },
    system: {
      // 确保所有 system 相关的属性都被初始化，即使 supports 中没有明确列出
      userList: [],
      sysPassword: "",
      reSysPassword: "",
      swPort: [],
      swPoe: [],
      swStorm: [],
      swQos: [],
      swVlan: [],
      sysSave: 0,
      led: { mode: "", beginTime: "", endTime: "" }, // 始终初始化 led
      reboot: { week: "", rateDelay: 0, time: "", enabled: 0 } // 始终初始化 reboot
    }
  };

  // 只有支持WAN时才初始化 wan 数组
  if (supports?.network?.supports?.includes("wan")) {
    status.network.wan = [
      {
        proto: "dhcp",
        username: "",
        password: "",
        ipaddr: "",
        netmask: "",
        gawa: "",
        dns1: "",
        dns2: ""
      }
    ];
  }

  return status;
}
