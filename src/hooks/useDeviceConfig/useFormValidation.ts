import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { deviceConfig, originalData, getDiff, submitConfig, drawerProps } from "@/api/interface/deviceConfigDrawer";

/**
 * 表单验证和提交管理
 */
export function useFormValidation() {
  const { t } = useI18n();

  // 密码输入
  const password = ref("");

  // 检查是否应该显示端口设置
  const shouldShowPortSettings = computed(() => {
    const row = drawerProps.value?.row;
    if (!row) return false;

    // 检查设备是否支持端口配置
    const systemSupports = row.supports?.system?.supports || [];
    const networkSupports = row.supports?.network?.supports || [];

    const hasPortSupport = ["swPort", "swPoe", "swVlan", "swStorm", "swIsolate", "swQos"].some(
      feature => systemSupports.includes(feature) || networkSupports.includes(feature)
    );

    return hasPortSupport;
  });

  // 表单验证
  const validateForm = () => {
    // SSID验证
    const needCheckRadio0 = deviceConfig.wireless?.radio0 && Object.keys(deviceConfig.wireless.radio0).length > 0;
    const needCheckRadio1 = deviceConfig.wireless?.radio1 && Object.keys(deviceConfig.wireless.radio1).length > 0;
    const needCheckGuest = deviceConfig.wireless?.guest && Object.keys(deviceConfig.wireless.guest).length > 0;
    const needCheckBrAp = deviceConfig.network?.brAp && Object.keys(deviceConfig.network.brAp).length > 0;

    if (
      (needCheckRadio0 && deviceConfig.wireless.radio0.disabled === 0 && !deviceConfig.wireless.radio0.ssid) ||
      (needCheckRadio1 && deviceConfig.wireless.radio1.disabled === 0 && !deviceConfig.wireless.radio1.ssid) ||
      (needCheckGuest && deviceConfig.wireless.guest.disabled === 0 && !deviceConfig.wireless.guest.ssid) ||
      (needCheckBrAp && !deviceConfig.network.brAp.ssid)
    ) {
      ElMessage.warning(t("device.ssidRequired") || "SSID不能为空");
      return false;
    }

    // LED定时模式校验
    if (deviceConfig.system?.led?.mode === "timer" && (!deviceConfig.system.led.beginTime || !deviceConfig.system.led.endTime)) {
      ElMessage.warning(t("device.ledTimeRequiredTip") || "请填写LED定时的开始和结束时间");
      return false;
    }

    return true;
  };

  // 主表单提交
  const handleMainSubmit = async () => {
    if (!validateForm()) {
      return false;
    }

    // 获取配置差异
    const diff = getDiff(originalData.value, deviceConfig);

    if (!diff) {
      ElMessage.warning(t("common.noConfigChanges"));
      return false;
    }

    // 先拷贝 diff，后续可能补字段
    let configData = JSON.parse(JSON.stringify(diff));

    // 检查 network.dhcp.dnsenabled/dns1/dns2 是否有变化
    if (!configData.network) configData.network = {};
    if (!configData.network.dhcp) configData.network.dhcp = {};

    // 这里可以添加更多的配置处理逻辑

    console.log("主表单提交配置数据:", configData);
    return await submitConfig(configData);
  };

  // 检查配置是否有变化
  const hasConfigChanged = computed(() => {
    const diff = getDiff(originalData.value, deviceConfig);
    return !!diff;
  });

  return {
    password,
    shouldShowPortSettings,
    validateForm,
    handleMainSubmit,
    hasConfigChanged
  };
}
