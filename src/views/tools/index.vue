<template>
  <div class="tools-container">
    <div class="table-box">
      <div class="avatar-div" @click="showDialog(0, t('common.ipSearch'))">
        <el-avatar :src="ipSearch" size="large" alt="IPsearch" shape="square" :class="avatarClass" />
        <el-text>{{ $t("common.ipSearch") }}</el-text>
      </div>
      <div class="avatar-div" @click="showDialog(1, $t('common.macSearch'))">
        <el-avatar :src="macQuery" size="large" alt="macQuery" shape="square" :class="avatarClass" />
        <el-text>{{ $t("common.macSearch") }}</el-text>
      </div>
      <div class="avatar-div" @click="showDialog(2, $t('common.pingTest'))">
        <el-avatar :src="pingTest" size="large" alt="pingTest" shape="square" :class="avatarClass" />
        <el-text>{{ $t("common.pingTest") }}</el-text>
      </div>
      <div class="avatar-div" @click="showDialog(3, $t('common.portScan'))">
        <el-avatar :src="portScan" size="large" alt="portScan" shape="square" :class="avatarClass" />
        <el-text>{{ $t("common.portScan") }}</el-text>
      </div>
      <div class="avatar-div" @click="showDialog(4, $t('common.bridgeHeightCalculator'))">
        <el-avatar :src="bridgeHeightCalculator" size="large" alt="portScan" shape="square" :class="avatarClass" />
        <el-text>{{ $t("common.bridgeHeightCalculator") }}</el-text>
      </div>

      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        :width="dialogWidth"
        @close="resetDialog"
        class="tools-dialog"
        append-to-body
        :close-on-click-modal="false"
      >
        <IpSearchComponent ref="ipSearchComponent" v-if="toolIndex === 0" :current-ip="currentIp" />
        <MacSearchComponent ref="macSearchComponent" v-if="toolIndex === 1" />
        <PingComponent ref="pingComponent" v-if="toolIndex === 2" />
        <PortScanComponent ref="portScanComponent" v-if="toolIndex === 3" />
        <BridgeHeightCalcuComponent ref="bridgeHeightCalcuComponent" v-if="toolIndex === 4" />
      </el-dialog>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import ipSearch from "@/assets/images/IPsearch.svg";
import macQuery from "@/assets/images/mac.svg";
import pingTest from "@/assets/images/ping.svg";
import portScan from "@/assets/images/port_scan.svg";
import bridgeHeightCalculator from "@/assets/images/bridge_height_calculator.svg";
import { useI18n } from "vue-i18n";
import IpSearchComponent from "@/views/tools/components/IpSearchComponent.vue";
import MacSearchComponent from "@/views/tools/components/MacSearchComponents.vue";
import PingComponent from "@/views/tools/components/PingComponent.vue";
import type { ComponentPublicInstance } from "vue";
import PortScanComponent from "@/views/tools/components/PortScanComponent.vue"; // 引入 PortScanComponent
import BridgeHeightCalcuComponent from "@/views/tools/components/BridgeHeightCalcuComponent.vue";

const { t } = useI18n();

const dialogVisible = ref(false);
const dialogTitle = ref("");
const toolIndex = ref(0);
const currentIp = ref("");
// const currentLocation = ref("");
// const flagUrl = ref<string | null>(null);
const searchResult = ref({
  ip: "",
  country: "",
  city: "",
  isp: ""
});

// 获取组件实例
interface IpSearchComponentInstance extends ComponentPublicInstance {
  resetState: () => void;
}

interface PingComponentInstance extends ComponentPublicInstance {
  resetState: () => void;
}

interface MacSearchComponentInstance extends ComponentPublicInstance {
  resetState: () => void;
}

interface PortScanComponentInstance extends ComponentPublicInstance {
  resetForm: () => void;
}

const ipSearchComponent = ref<IpSearchComponentInstance | null>(null);
const pingComponent = ref<PingComponentInstance | null>(null);
const macSearchComponent = ref<MacSearchComponentInstance | null>(null);
const portScanComponent = ref<PortScanComponentInstance | null>(null);

// 响应式对话框宽度和图标class
const screenWidth = ref(window.innerWidth);

const dialogWidth = computed(() => {
  if (screenWidth.value <= 480) return "95vw";
  if (screenWidth.value <= 768) return "85vw";
  return "60%";
});

const avatarClass = computed(() => {
  if (screenWidth.value <= 480) return "avatar-mobile";
  if (screenWidth.value <= 768) return "avatar-tablet";
  return "avatar-desktop";
});

const handleResize = () => {
  screenWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 回调方法
const showDialog = (index: number, title: string) => {
  dialogVisible.value = true;
  dialogTitle.value = title;
  toolIndex.value = index;

  if (index === 0) {
    // currentIp.value = "";
    // currentLocation.value = "";
    // flagUrl.value = null;
    searchResult.value = {
      ip: "",
      country: "",
      city: "",
      isp: ""
    };
  }
};

// 关闭对话框时重置组件状态
const resetDialog = () => {
  dialogVisible.value = false;

  // 根据当前工具索引重置相应组件
  switch (toolIndex.value) {
    case 0:
      ipSearchComponent.value?.resetState();
      break;
    case 1:
      macSearchComponent.value?.resetState?.();
      break;
    case 2:
      pingComponent.value?.resetState();
      break;
    case 3:
      portScanComponent.value?.resetForm?.();
      break;
  }

  // toolIndex.value = 0;
};
</script>
<style scoped>
@import "./index";
</style>
