import { reactive, ref } from "vue";
import { Configuration } from "@/api/interface/configuration";
import type { Project } from "@/api/interface/project";

export interface SupportItem {
  supports: any; // 根据实际情况定义更具体的类型
}

export interface Supports {
  wireless?: SupportItem;
  network?: SupportItem;
  system?: SupportItem;
}

export interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<Project.ResDeviceList>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
  projectId?: string;
  isEditing?: boolean;
}

export const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {
    deviceName: "",
    deviceType: "",
    deviceModel: "",
    mac: "",
    ipaddr: "",
    status: 0,
    bootTime: 0,
    supports: {}
  },
  isEditing: false
});

export interface DeviceConfigData {
  wireless?: any;
  network?: any;
  system?: any;
}

export const deviceConfig = reactive<Configuration.DeviceConfig & { deviceName?: string }>({
  deviceName: undefined,
  network: {
    wan: undefined,
    lan: {
      netmask: undefined,
      ipaddr: undefined
    },
    wanMax: undefined,
    workmode: undefined,
    brAp: {
      ssid: undefined,
      key: undefined,
      hidden: undefined,
      mode: undefined,
      channel: undefined,
      txpower: undefined,
      chanList: undefined
    },
    dhcp: {
      dnsenabled: undefined,
      dns1: undefined,
      dns2: undefined,
      start: undefined,
      limit: undefined
    },
    join: {
      ssid: undefined,
      key: undefined,
      radio: undefined
    },
    brSafe: {
      mode: undefined,
      status: undefined,
      timedown: undefined,
      time: undefined
    },
    swRstp: undefined,
    swLldp: undefined,
    swVlan: undefined,
    swIsolate: undefined
  },
  system: {
    /* reboot: {
      week: undefined,
      rateDelay: undefined,
      time: undefined,
      enabled: undefined
    }, */
    userList: undefined,
    led: {
      mode: undefined,
      beginTime: undefined,
      endTime: undefined
    },
    sysPassword: undefined,
    reSysPassword: undefined,
    swPort: undefined,
    swPoe: undefined,
    swVlan: undefined,
    swQos: undefined,
    sysSave: undefined,
    swStorm: undefined
  }
});

export enum SpeedDuplex {
  TenHalf = 0, // 10h
  TenFull = 1, // 10f
  HundredHalf = 2, // 100h
  HundredFull = 3, // 100f
  ThousandFull = 4, // 1000f
  TwoThousandFiveFull = 5, // 2500f
  FiveThousandFull = 6, // 5000f
  TenThousandFull = 7 // 10000f
}

export let swPort = ref<Configuration.swPort>({
  describe: undefined,
  extend: undefined,
  flwctrl: undefined,
  link: undefined,
  name: undefined,
  port: undefined,
  speed_duplex: undefined,
  portenable: undefined,
  autoneg: undefined
});

export let swPoe = ref<Configuration.swPoe>({
  name: undefined,
  port: undefined,
  poeenable: undefined,
  poeclass: undefined,
  power: undefined,
  powerout: undefined,
  totalpower: undefined,
  poewd: undefined,
  poetime: undefined
});

export let swStorm = ref<Configuration.swStorm>({
  trafficType: undefined,
  name: undefined,
  port: undefined,
  rate1: undefined,
  rate2: undefined,
  rate3: undefined
});

export let swVlan = ref<Configuration.swVlan>({
  port: undefined,
  name: undefined,
  vlanmode: undefined,
  pvid: undefined,
  permit: undefined,
  untag: undefined
});

export let swIsolate = ref<Configuration.swIsolate>({
  downport: undefined,
  isolate: undefined,
  name: undefined
});

export let swQos = ref<Configuration.swQos>({
  port: undefined,
  name: undefined,
  qos: undefined
});

export interface DeviceStatistics {
  date: string;
  rxByte: number;
  txByte: number;
  hour?: null;
}

export interface DeviceWeekStatistics {
  deviceId: string;
  deviceName: string;
  beginDate: string;
  beginDateTimeZone?: null;
  endDate: string;
  endDateTimeZone?: null;
  bootTime: number;
  deviceType: string;
  deviceModel: string;
  rxByte: number;
  txByte: number;
  statistics: DeviceStatistics[];
}

export const deviceStatus = reactive<Partial<Configuration.DeviceConfig>>({
  wireless: {
    wifiTime: {
      enabled: undefined,
      week: undefined,
      beginTime: undefined,
      endTime: undefined
    },
    radio1: {
      hidden: undefined,
      chanList: undefined,
      txpower: undefined,
      channel: undefined,
      disabled: undefined,
      ssid: undefined,
      key: undefined
    },
    radio0: {
      hidden: undefined,
      chanList: undefined,
      txpower: undefined,
      channel: undefined,
      disabled: undefined,
      ssid: undefined,
      key: undefined
    },
    guest: {
      rate: undefined,
      wifiTime: undefined,
      disabled: undefined,
      ssid: undefined,
      key: undefined,
      hidden: undefined
    }
  },
  network: {
    wan: undefined,
    lan: {
      netmask: undefined,
      ipaddr: undefined
    },
    wanMax: undefined,
    workmode: undefined,
    brAp: {
      ssid: undefined,
      key: undefined,
      hidden: undefined,
      mode: undefined,
      channel: undefined,
      txpower: undefined,
      chanList: undefined
    },
    dhcp: {
      dnsenabled: undefined,
      dns1: undefined,
      dns2: undefined,
      start: undefined,
      limit: undefined
    },
    join: {
      ssid: undefined,
      key: undefined,
      radio: undefined
    },
    brSafe: {
      mode: undefined,
      status: undefined,
      timedown: undefined,
      time: undefined
    },
    swRstp: undefined,
    swLldp: undefined,
    swVlan: undefined,
    swIsolate: undefined
  },
  system: {
    /* reboot: {
      week: undefined,
      rateDelay: undefined,
      time: undefined,
      enabled: undefined
    }, */
    userList: undefined,
    led: {
      mode: undefined,
      beginTime: undefined,
      endTime: undefined
    },
    sysPassword: undefined,
    reSysPassword: undefined,
    swPort: undefined,
    swPoe: undefined,
    swVlan: undefined,
    swQos: undefined,
    sysSave: undefined,
    swStorm: undefined
  }
});
