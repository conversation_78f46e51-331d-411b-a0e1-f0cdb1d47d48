# Geeker-Admin 项目指南

## 项目概览

**项目名称:** geeker-admin
**版本:** 1.2.0
**描述:** geeker-admin open source management system

## 快速开始

### 安装依赖

本项目使用 `pnpm` 作为包管理器。请确保您已安装 `pnpm`。

```bash
pnpm install
```

### 运行开发服务器

```bash
pnpm run dev
# 或者
pnpm run serve
```

### 构建项目

- **开发环境构建:**
  ```bash
  pnpm run build:dev
  # 或者跳过类型检查
  pnpm run build:dev:skip-ts
  ```

- **测试环境构建:**
  ```bash
  pnpm run build:test
  # 或者跳过类型检查
  pnpm run build:test:skip-ts
  ```

- **生产环境构建:**
  ```bash
  pnpm run build:pro
  # 或者跳过类型检查
  pnpm run build:pro:skip-ts
  ```

### 预览构建产物

```bash
pnpm run preview
```

## 代码质量

### 运行所有代码检查

```bash
pnpm run lint
```

### 运行 ESLint 检查并自动修复

```bash
pnpm run lint:eslint
```

### 运行 Prettier 格式化并自动修复

```bash
pnpm run lint:prettier
```

### 运行 Stylelint 检查并自动修复

```bash
pnpm run lint:stylelint
```

### 运行类型检查

```bash
pnpm run type:check
```

## 贡献指南

### Git Hooks

本项目使用 Husky 管理 Git Hooks。在提交代码前，会自动运行 `lint-staged`。

### 提交规范

本项目使用 `czg` 进行规范化提交。请使用以下命令进行提交：

```bash
pnpm run commit
```

## 与 Gemini CLI 交互

以下是一些您可以使用 Gemini CLI 在本项目中执行的常见操作示例：

- **启动开发服务器:**
  ```
  pnpm run dev
  ```

- **运行所有 lint 检查:**
  ```
  pnpm run lint
  ```

- **查找包含特定内容的 Vue 文件:**
  ```
  search_file_content "<template>" include="*.vue" path="src/views"
  ```

- **读取 `PortSettings.vue` 文件内容:**
  ```
  read_file absolute_path="D:/WebstormProjects/Geeker-Admin/src/views/project/components/PortSettings.vue"
  ```

- **列出 `src/api` 目录下的所有文件:**
  ```
  list_directory path="D:/WebstormProjects/Geeker-Admin/src/api"
  ```

- **查找所有 `.scss` 样式文件:**
  ```
  glob pattern="**/*.scss"
  ```

- **修复 `PortSettings.vue` 中的某个具体错误 (示例，请根据实际错误调整):**
  ```
  replace file_path="D:/WebstormProjects/Geeker-Admin/src/views/project/components/PortSettings.vue" old_string="旧代码片段" new_string="新代码片段"
  ```

请根据您的具体需求和项目上下文，灵活运用上述命令。