import { ref, onMounted, onUnmounted } from "vue";
import { deviceWeekStatistic } from "@/api/interface/deviceConfigDrawer";

/**
 * 图表数据管理
 */
export function useChartData() {
  // D3图表数据
  const chartData = ref<Array<{ date: string; rxByte: number; txByte: number }>>([]);
  const chartWidth = ref(640);
  const chartHeight = ref(400);

  // 异步更新图表数据
  const updateChartData = () => {
    // 从deviceWeekStatistic中获取数据
    if (deviceWeekStatistic.value?.statistics) {
      // 转换数据格式为D3需要的格式
      chartData.value = deviceWeekStatistic.value.statistics.map(item => ({
        date: item.date,
        rxByte: item.rxByte,
        txByte: item.txByte
      }));
    }
  };

  // 响应式调整图表尺寸
  const updateChartSize = () => {
    const width = window.innerWidth;
    if (width <= 768) {
      chartWidth.value = Math.max(300, width - 100);
      chartHeight.value = 300;
    } else if (width <= 1024) {
      chartWidth.value = 500;
      chartHeight.value = 350;
    } else {
      chartWidth.value = 640;
      chartHeight.value = 400;
    }
  };

  // 监听窗口大小变化
  const handleResize = () => {
    updateChartSize();
  };

  onMounted(() => {
    updateChartSize();
    window.addEventListener("resize", handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
  });

  return {
    chartData,
    chartWidth,
    chartHeight,
    updateChartData,
    updateChartSize
  };
}
