<template>
  <el-card shadow="always" :style="{ marginTop: '10px', marginBottom: '10px' }" class="box-card" v-if="show">
    <el-header>
      <el-form-item :label="$t('device.timeSwitch')" label-position="left">
        <el-switch
          v-model="localWifiTime.enabled"
          active-color="#13ce66"
          inactive-color="#ff4949"
          :active-value="1"
          :inactive-value="0"
          :active-text="$t('device.open')"
          :inactive-text="$t('device.close')"
        />
      </el-form-item>
    </el-header>
    <el-main v-if="localWifiTime.enabled === 1">
      <el-form-item :label="$t('device.timingTime')" label-position="left" label-width="160px" prop="wireless.wifiTime.week">
        <el-select v-model="localWifiTime.week" :placeholder="$t('device.timingTimePlaceholder')">
          <el-option :label="$t('device.everyday')" :value="'*'" />
          <el-option :label="$t('device.sunday')" :value="'0'" />
          <el-option :label="$t('device.monday')" :value="'1'" />
          <el-option :label="$t('device.tuesday')" :value="'2'" />
          <el-option :label="$t('device.wednesday')" :value="'3'" />
          <el-option :label="$t('device.thursday')" :value="'4'" />
          <el-option :label="$t('device.friday')" :value="'5'" />
          <el-option :label="$t('device.saturday')" :value="'6'" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('device.startupTime')" label-position="left" label-width="160px" prop="wireless.wifiTime.endTime">
        <el-time-picker
          v-model="localWifiTime.endTime"
          format="HH:mm"
          value-format="HH:mm"
          :placeholder="t('device.beginTimePlaceholder')"
          :clearable="false"
        ></el-time-picker>
      </el-form-item>
      <el-form-item
        :label="$t('device.closingTime')"
        label-position="left"
        label-width="160px"
        prop="wireless.wifiTime.beginTime"
      >
        <el-time-picker
          v-model="localWifiTime.beginTime"
          format="HH:mm"
          value-format="HH:mm"
          :placeholder="t('device.endTimePlaceholder')"
          :clearable="false"
        />
      </el-form-item>
    </el-main>
  </el-card>
</template>

<script setup>
import { computed } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  supports: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(["update:modelValue"]);

const localWifiTime = computed({
  get: () => props.modelValue,
  set: val => emit("update:modelValue", val)
});

const show = computed(() => props.supports?.supports?.includes("wifiTime") && props.modelValue);
</script>
