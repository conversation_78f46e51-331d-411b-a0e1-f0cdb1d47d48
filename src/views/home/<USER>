/* 暗黑模式样式 */
html.dark {
  .main-box {
    &::before {
      background: radial-gradient(circle, rgb(70 130 180 / 30%) 0%, rgb(70 130 180 / 0%) 70%);
    }
    &::after {
      background: radial-gradient(circle, rgb(255 100 100 / 30%) 0%, rgb(255 100 100 / 0%) 70%);
    }
    .main-content {
      .box-card {
        background: rgb(30 30 30 / 30%);
        border: 1px solid rgb(255 255 255 / 10%);
        &:hover {
          background: rgb(40 40 40 / 40%);
          box-shadow: 0 12px 32px rgb(0 0 0 / 30%);
        }
      }
      .chart-container {
        &::before {
          background: linear-gradient(135deg, rgb(40 40 40 / 10%) 0%, rgb(30 30 30 / 20%) 100%);
        }
      }
      .message-container {
        &::before {
          background: linear-gradient(135deg, rgb(40 40 40 / 10%) 0%, rgb(30 30 30 / 20%) 100%);
        }
        &::-webkit-scrollbar-thumb {
          background-color: rgb(255 255 255 / 10%);
          &:hover {
            background-color: var(--el-color-primary-dark-2);
          }
        }
        &::-webkit-scrollbar-track {
          background-color: rgb(0 0 0 / 20%);
        }
      }
      .message-card {
        background: rgb(30 30 30 / 30%);
        border: 1px solid rgb(255 255 255 / 10%);
        &:hover {
          background: rgb(40 40 40 / 40%);
          border-color: var(--el-color-primary-dark-2);
          box-shadow: 0 8px 20px rgb(0 0 0 / 30%);
        }
        .message-header {
          h4 {
            color: var(--el-text-color-primary);
          }
          .message-time {
            color: var(--el-text-color-secondary);
          }
        }
        .message-content p {
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}
.main-box {
  position: relative;
  display: flex;
  height: 100%;
  overflow: hidden;
  background: var(--el-bg-color-page);

  /* 装饰元素 - 左下角光晕 */
  &::before {
    position: absolute;
    bottom: -150px;
    left: -150px;
    z-index: 0;
    width: 400px;
    height: 400px;
    content: "";
    background: radial-gradient(circle, rgb(173 216 230 / 50%) 0%, rgb(173 216 230 / 0%) 70%);
    opacity: 0.6;
  }

  /* 装饰元素 - 右上角光晕 */
  &::after {
    position: absolute;
    top: -100px;
    right: -100px;
    z-index: 0;
    width: 300px;
    height: 300px;
    content: "";
    background: radial-gradient(circle, rgb(255 182 193 / 50%) 0%, rgb(255 182 193 / 0%) 70%);
    opacity: 0.4;
  }
  .main-content {
    position: relative;
    z-index: 1;
    flex: 1;
    padding: 24px;
    overflow: auto;

    // 首页行响应式优化
    .home-row {
      margin: 0 -10px;
    }
    .chart-col,
    .message-col {
      padding: 0 10px;
      margin-bottom: 20px;
    }
    .box-card {
      display: flex;
      flex-direction: column;
      height: 100%;

      /* 使用毛玻璃效果类的样式，但不使用!important以允许暗黑模式覆盖 */
      background: rgb(255 255 255 / 10%);
      backdrop-filter: blur(10px);
      border: 1px solid rgb(255 255 255 / 18%);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgb(0 0 0 / 10%);
      transition: all 0.3s ease;
      &:hover {
        background: rgb(255 255 255 / 20%);
        box-shadow: 0 12px 32px rgb(0 0 0 / 15%);
        transform: translateY(-5px);
      }
      .el-card__header {
        flex-shrink: 0;
        padding: 20px 24px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        .clearfix {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
          align-items: center;
          justify-content: space-between;
          .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
          }
          span {
            position: relative;
            padding-left: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              width: 4px;
              height: 16px;
              content: "";
              background: var(--el-color-primary);
              border-radius: 2px;
              transform: translateY(-50%);
            }
          }
          .el-button {
            padding: 8px 16px;
            font-weight: 500;
            white-space: nowrap;
            border-radius: 8px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
              transform: translateY(-1px);
            }
            &.action-button {
              position: relative;
              padding: 10px 20px;
              overflow: hidden;
              font-size: 15px;
              font-weight: 600;
              letter-spacing: 0.5px;
              border-radius: 10px;
              box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
              &::before {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                content: "";
                background: linear-gradient(
                  90deg,
                  rgb(255 255 255 / 0%) 0%,
                  rgb(255 255 255 / 20%) 50%,
                  rgb(255 255 255 / 0%) 100%
                );
                transition: all 0.8s ease;
              }
              &:hover {
                box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.5);
                transform: translateY(-3px);
                &::before {
                  left: 100%;
                }
              }
              .el-icon {
                margin-right: 6px;
                font-size: 16px;
                transition: all 0.3s ease;
              }
              &:hover .el-icon {
                transform: scale(1.2);
              }

              // 暗色模式适配
              .dark & {
                box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.2);
                &::before {
                  background: linear-gradient(90deg, rgb(0 0 0 / 0%) 0%, rgb(255 255 255 / 10%) 50%, rgb(0 0 0 / 0%) 100%);
                }
                &:hover {
                  box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.3);
                }
              }
            }
            &.refresh-button {
              margin-right: 10px;
            }
          }
        }
      }
      .el-card__body {
        box-sizing: border-box;
        display: flex;
        flex: 1;
        min-height: 0;
        padding: 24px;
      }
    }

    // 移动端响应式优化
    @media screen and (width <= 768px) {
      padding: 16px;
      .home-row {
        margin: 0 -8px;
      }
      .chart-col,
      .message-col {
        padding: 0 8px;
        margin-bottom: 16px;
      }
      .box-card {
        border-radius: 12px;
        .el-card__header {
          padding: 16px 20px;
          .clearfix {
            flex-direction: column;
            gap: 12px;
            align-items: flex-start;
            span {
              font-size: 15px;
            }
            .el-button.action-button {
              align-self: flex-end;
              padding: 8px 16px;
              font-size: 14px;
            }
          }
        }
        .el-card__body {
          padding: 20px;
        }
      }
    }

    @media screen and (width <= 480px) {
      padding: 12px;
      .home-row {
        margin: 0 -6px;
      }
      .chart-col,
      .message-col {
        padding: 0 6px;
        margin-bottom: 12px;
      }
      .box-card {
        border-radius: 10px;
        .el-card__header {
          padding: 12px 16px;
          .clearfix {
            gap: 8px;
            span {
              padding-left: 10px;
              font-size: 14px;
              &::before {
                width: 3px;
                height: 14px;
              }
            }
            .el-button.action-button {
              padding: 6px 12px;
              font-size: 13px;
              letter-spacing: 0.3px;
            }
          }
        }
        .el-card__body {
          padding: 16px;
        }
      }
    }
  }
}

/* 确保两个卡片高度一致的共享样式 */
.chart-container,
.message-container {
  position: relative;
  box-sizing: border-box;
  flex: 1;
  width: 100%;
  height: 400px;
  margin: 0;

  /* 添加微妙的背景效果 */
  &::before {
    position: absolute;
    inset: 0;
    z-index: -1;
    content: "";
    background: linear-gradient(135deg, rgb(255 255 255 / 5%) 0%, rgb(255 255 255 / 10%) 100%);
    border-radius: 8px;
  }

  // 移动端高度优化
  @media screen and (width <= 768px) {
    height: 350px;
  }

  @media screen and (width <= 480px) {
    height: 300px;
  }
}

/* 消息容器特有样式 */
.message-container {
  padding: 0;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
    border-radius: 3px;
    &:hover {
      background-color: var(--el-color-primary-light-5);
    }
  }
  &::-webkit-scrollbar-track {
    background-color: var(--el-border-color-lighter);
    border-radius: 3px;
  }

  // 移动端滚动条优化
  @media screen and (width <= 768px) {
    &::-webkit-scrollbar {
      width: 4px;
    }
  }
}
.message-card {
  margin-bottom: 16px;
  background: rgb(255 255 255 / 10%);
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 18%);
  border-radius: 12px;
  transition: all 0.3s ease;
  &:hover {
    background: rgb(255 255 255 / 20%);
    border-color: var(--el-color-primary-light-7);
    box-shadow: 0 8px 20px rgb(0 0 0 / 10%);
    transform: translateY(-3px);
  }
  .message-header {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    h4 {
      flex: 1;
      min-width: 0;
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .message-time {
      font-size: 13px;
      color: var(--el-text-color-secondary);
      white-space: nowrap;
    }
  }
  .message-content {
    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
      color: var(--el-text-color-primary);
      word-break: break-word;
    }
  }

  // 移动端消息卡片优化
  @media screen and (width <= 768px) {
    margin-bottom: 12px;
    border-radius: 10px;
    .message-header {
      margin-bottom: 10px;
      h4 {
        font-size: 13px;
        line-height: 1.4;
      }
      .message-time {
        font-size: 12px;
      }
    }
    .message-content p {
      font-size: 13px;
      line-height: 1.5;
    }
  }

  @media screen and (width <= 480px) {
    margin-bottom: 10px;
    border-radius: 8px;
    .message-header {
      flex-direction: column;
      gap: 4px;
      align-items: flex-start;
      margin-bottom: 8px;
      h4 {
        width: 100%;
        font-size: 12px;
      }
      .message-time {
        align-self: flex-end;
        font-size: 11px;
      }
    }
    .message-content p {
      font-size: 12px;
      line-height: 1.4;
    }
  }
}
:deep(.el-timeline-item__node) {
  background-color: var(--el-color-primary-light-7);
  border-color: var(--el-color-primary);
}
:deep(.el-timeline-item__tail) {
  border-left-color: var(--el-border-color-lighter);
}
:deep(.el-timeline-item__timestamp) {
  font-size: 13px;
  color: var(--el-text-color-secondary);

  @media screen and (width <= 768px) {
    font-size: 12px;
  }

  @media screen and (width <= 480px) {
    font-size: 11px;
  }
}

// Timeline 移动端优化
@media screen and (width <= 768px) {
  :deep(.el-timeline-item) {
    padding-bottom: 16px;
  }
  :deep(.el-timeline-item__wrapper) {
    padding-left: 20px;
  }
  :deep(.el-timeline-item__node) {
    left: -5px;
    width: 10px;
    height: 10px;
  }
}

@media screen and (width <= 480px) {
  :deep(.el-timeline-item) {
    padding-bottom: 14px;
  }
  :deep(.el-timeline-item__wrapper) {
    padding-left: 18px;
  }
  :deep(.el-timeline-item__node) {
    left: -4px;
    width: 8px;
    height: 8px;
  }
}

// 悬浮APP下载按钮
.floating-app-download {
  position: fixed;
  right: 24px;
  bottom: 80px;
  z-index: 1000;
  .floating-qr-code {
    :deep(.qr-trigger) {
      width: 48px;
      height: 48px;
      background: var(--el-color-primary);
      border: none;
      border-radius: 50%;
      box-shadow: 0 4px 12px rgb(64 158 255 / 40%);
      transition: all 0.3s ease;
      &:hover {
        background: var(--el-color-primary-dark-2);
        box-shadow: 0 6px 20px rgb(64 158 255 / 60%);
        transform: translateY(-2px) scale(1.05);
      }
      .qr-icon {
        font-size: 20px;
        color: white;
      }
    }
  }
}

// 移动端适配
@media screen and (width <= 768px) {
  .floating-app-download {
    right: 16px;
    bottom: 60px;
    .floating-qr-code {
      :deep(.qr-trigger) {
        width: 42px;
        height: 42px;
        .qr-icon {
          font-size: 18px;
        }
      }
    }
  }
}
