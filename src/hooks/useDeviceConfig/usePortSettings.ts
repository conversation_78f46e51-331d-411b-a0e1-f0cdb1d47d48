import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import {
  swPort,
  swPoe,
  swVlan,
  swStorm,
  swIsolate,
  swQos,
  selectedRows,
  portDialogVisible,
  showPortDialog,
  submitConfig,
  generatePortData
} from "@/api/interface/deviceConfigDrawer";

/**
 * 端口设置管理
 */
export function usePortSettings() {
  const { t } = useI18n();

  // 端口状态
  const portStates = ref<any[]>([]);

  // 对话框标签页激活状态
  const dialogTabActive = ref("first");

  // VLAN字符串处理
  const permitVlanString = ref("");
  const untagVlanString = ref("");

  // 监听swVlan变化，更新字符串显示
  watch(
    () => swVlan.value,
    newVal => {
      if (newVal.permit && Array.isArray(newVal.permit)) {
        permitVlanString.value = newVal.permit.join(",");
      } else {
        permitVlanString.value = "";
      }

      if (newVal.untag && Array.isArray(newVal.untag)) {
        untagVlanString.value = newVal.untag.join(",");
      } else {
        untagVlanString.value = "";
      }
    },
    { immediate: true }
  );

  // 更新 VLAN 数组
  const updatePermitVlan = () => {
    if (!permitVlanString.value) {
      swVlan.value.permit = [];
      return;
    }

    swVlan.value.permit = permitVlanString.value
      .split(",")
      .map(item => {
        const num = parseInt(item.trim(), 10);
        return isNaN(num) ? 0 : num;
      })
      .filter(num => num > 0 && num <= 4094);
  };

  const updateUntagVlan = () => {
    if (!untagVlanString.value) {
      swVlan.value.untag = [];
      return;
    }

    const values = untagVlanString.value
      .split(",")
      .map(item => {
        const num = parseInt(item.trim(), 10);
        return isNaN(num) ? 0 : num;
      })
      .filter(num => num > 0 && num <= 4094);

    swVlan.value.untag = values;
  };

  // 打开端口对话框
  const openPortDialog = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning(t("device.selectPortTip"));
      return;
    }
    showPortDialog();
    // 弹出层打开后，加载当前活跃标签页的数据
    await loadDialogTabData(dialogTabActive.value);
  };

  // 端口配置标签页的加载状态
  const portSettingsLoading = ref(false);

  // 加载对话框标签页数据的占位函数
  const loadDialogTabData = async (tabName: string) => {
    console.log(`加载对话框标签页数据: ${tabName}`);
    // 这里应该包含实际的数据加载逻辑
  };

  // 处理对话框提交
  const handleDialogSubmit = async () => {
    let configData = null;

    // 如果没有选中端口，直接使用当前标签页的数据
    if (!selectedRows.value || selectedRows.value.length === 0) {
      console.log("handleSubmit: 没有选中端口，使用当前标签页数据");
      switch (dialogTabActive.value) {
        case "first":
          configData = { system: { swPort: [swPort.value] } };
          break;
        case "second":
          configData = { system: { swPoe: [swPoe.value] } };
          break;
        case "third":
          configData = { network: { swVlan: [swVlan.value] } };
          break;
        case "fourth":
          configData = { system: { swStorm: [swStorm.value] } };
          break;
        case "fifth":
          configData = { network: { swIsolate: [swIsolate.value] } };
          break;
        case "sixth":
          configData = { system: { swQos: [swQos.value] } };
          break;
      }
    } else {
      // 有选中端口时，使用 generatePortData
      switch (dialogTabActive.value) {
        case "first":
          configData = { system: { swPort: generatePortData(swPort.value) } };
          break;
        case "second":
          configData = { system: { swPoe: generatePortData(swPoe.value) } };
          break;
        case "third":
          configData = { network: { swVlan: generatePortData(swVlan.value) } };
          break;
        case "fourth":
          configData = { system: { swStorm: generatePortData(swStorm.value) } };
          break;
        case "fifth":
          configData = { network: { swIsolate: generatePortData(swIsolate.value) } };
          break;
        case "sixth":
          configData = { system: { swQos: generatePortData(swQos.value) } };
          break;
      }
    }

    // 提交对话框配置
    console.log("handleSubmit: 准备提交的配置数据:", configData);
    const success = await submitConfig(configData);
    if (success) {
      portDialogVisible.value = false;
    }
    return success;
  };

  return {
    portStates,
    dialogTabActive,
    permitVlanString,
    untagVlanString,
    portSettingsLoading,
    updatePermitVlan,
    updateUntagVlan,
    openPortDialog,
    loadDialogTabData,
    handleDialogSubmit,
    // 导出端口相关的响应式变量
    swPort,
    swPoe,
    swVlan,
    swStorm,
    swIsolate,
    swQos,
    selectedRows,
    portDialogVisible
  };
}
