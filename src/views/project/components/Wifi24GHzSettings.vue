<template>
  <el-card
    class="box-card"
    shadow="always"
    :style="{ marginTop: '10px', marginBottom: '10px' }"
    v-if="supports?.includes('radio0') && modelValue"
  >
    <el-header>
      <el-form-item :label="$t('device.wifi24GHz')" label-position="left">
        <el-switch
          v-model="disabled"
          :active-text="$t('device.open')"
          :inactive-text="$t('device.close')"
          :active-value="0"
          :inactive-value="1"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </el-form-item>
    </el-header>
    <el-main v-if="disabled === 0">
      <el-form-item :label="$t('device.name')" label-position="left" label-width="160px" prop="ssid">
        <el-input v-model="ssid" clearable />
      </el-form-item>
      <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
        <el-switch v-model="hidden" :active-value="1" :inactive-value="0" />
      </el-form-item>
      <el-form-item :label="$t('device.encryptionMethod')" label-position="left" label-width="160px">
        <el-select v-model="encryptionMethod">
          <el-option :label="$t('device.encryption')" :value="true" />
          <el-option :label="$t('device.encryptionNone')" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('device.key')" label-position="left" label-width="160px" v-if="encryptionMethod" prop="key">
        <el-input type="password" v-model="key" show-password clearable @clear="handlePasswordClear" />
      </el-form-item>
      <el-form-item :label="$t('device.channel')" label-position="left" label-width="160px">
        <el-select v-model="channel">
          <el-option :label="t('common.auto')" :value="0" />
          <el-option v-for="channel in modelValue.chanList" :key="channel" :label="channel" :value="channel" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('device.txpower')" label-position="left" label-width="160px">
        <el-select v-model="txpower">
          <el-option
            v-for="percent in [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
            :key="percent"
            :label="`${percent}%`"
            :value="percent"
          />
        </el-select>
      </el-form-item>
    </el-main>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  supports: {
    type: Array,
    default: () => []
  }
});
const emit = defineEmits(["update:modelValue"]);
const updateModel = (updates: Partial<typeof props.modelValue>) => {
  emit("update:modelValue", { ...props.modelValue, ...updates });
};
const disabled = computed({
  get: () => props.modelValue.disabled ?? 1,
  set: val => updateModel({ disabled: val })
});
const ssid = computed({
  get: () => props.modelValue.ssid ?? "",
  set: val => updateModel({ ssid: val })
});
const hidden = computed({
  get: () => props.modelValue.hidden ?? 0,
  set: val => updateModel({ hidden: val })
});
const key = computed({
  get: () => props.modelValue.key ?? "",
  set: val => updateModel({ key: val })
});
const channel = computed({
  get: () => props.modelValue.channel ?? 0,
  set: val => updateModel({ channel: val })
});
const txpower = computed({
  get: () => props.modelValue.txpower ?? 100,
  set: val => updateModel({ txpower: val })
});
const encryptionMethod = computed({
  get: () => !!props.modelValue.key,
  set: val => {
    updateModel({ key: val ? props.modelValue.key : "" });
  }
});
const handlePasswordClear = () => {
  updateModel({ key: "" });
};
</script>
