.filter {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 220px;
  min-width: 220px;
  min-height: 160px;
  padding: 18px;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-info-dark-2);
  letter-spacing: 0.5px;
  background: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 12px 32px rgb(0 0 0 / 8%);
    transform: translateY(-2px);
  }
  .title {
    position: relative;
    padding-left: 12px;
    margin: 0 0 15px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    letter-spacing: 0.5px;
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 16px;
      content: "";
      background: var(--el-color-primary);
      border-radius: 2px;
      transform: translateY(-50%);
    }
  }
}
.online-text {
  color: green;
}
.offline-text {
  color: red;
}
.legend {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.legend-item {
  display: flex;
  align-items: center;
  margin: 0 10px;
}
.legend-line {
  display: inline-block;
  width: 30px;
  height: 3px;
  margin-right: 5px;
}
.legend-line.success {
  background-color: #00c6ff;
}
.legend-line.process {
  background-color: #ffbf00;
}
.legend-line.failed {
  background-color: #ff0000;
}
.card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 设备列表标题样式 */
.device-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 16px;
  margin-bottom: 10px;
  background: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  .title-section {
    display: flex;
    align-items: center;
    .font-bold {
      position: relative;
      padding-left: 12px;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      letter-spacing: 0.5px;
      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        content: "";
        background: var(--el-color-primary);
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }
  }
}

/* 设备列表容器样式 */
.device-list-container {
  flex: 1;
  padding: 0 4px;
  overflow-y: auto;
  scrollbar-width: thin;
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
    border-radius: 20px;
  }
  .box-card {
    margin-bottom: 12px;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 6px 16px rgb(0 0 0 / 8%);
      transform: translateY(-2px);
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.device-img {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
  object-fit: contain; /* 确保图片比例适配 */
}
.device-card {
  margin-bottom: 16px;
  background: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 12px;
  transition: all 0.3s ease;
  &:hover {
    border-color: var(--el-color-primary-light-7);
    box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
    transform: translateY(-2px);
  }
  .card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    strong {
      font-size: 15px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .el-icon {
      padding: 8px;
      color: var(--el-text-color-secondary);
      border-radius: 6px;
      transition: all 0.3s ease;
      &:hover {
        color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
        transform: rotate(90deg);
      }
    }
  }
  .el-row {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    margin-bottom: 32px;
    border-radius: 8px;
    transition: all 0.3s ease;
    &:hover {
      background: var(--el-color-primary-light-9);
    }
    &:last-child {
      margin-bottom: 0;
    }
    .el-col {
      width: 100%;
    }
    .el-text {
      display: flex;
      align-items: center;
      font-size: 14px;
      line-height: 1.8;
      color: var(--el-text-color-regular);
      strong {
        position: relative;
        padding-left: 12px;
        margin-right: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 2px;
          height: 14px;
          content: "";
          background: var(--el-color-primary);
          border-radius: 1px;
          transform: translateY(-50%);
        }
      }
    }
  }
  .online-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-success);
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 8px;
      content: "";
      background: var(--el-color-success);
      border-radius: 50%;
    }
  }
  .offline-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-danger);
    &::before {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 8px;
      content: "";
      background: var(--el-color-danger);
      border-radius: 50%;
    }
  }
}
.device-mac {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 12px;
  color: #999999;
}
.selected-card {
  box-shadow: 0 4px 8px rgb(0 0 0 / 20%);
}
.custom-dialog .dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.custom-dialog .dialog-body {
  flex: 1;
  min-height: 160px; /* 防止内容溢出 */
  overflow: auto;
}
.custom-dialog .dialog-footer {
  padding: 10px;
  text-align: right;

  // background-color: #ffffff; /* 如有需要可设置背景色 */
  border-top: 1px solid #ebeef5;
}
.main-box {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);
  }
  .main-content {
    flex: 1;
    overflow: auto;
    .box-card {
      min-height: 160px;
      background: var(--el-bg-color);
      border-radius: 16px;
      box-shadow: 0 8px 24px rgb(0 0 0 / 5%);
      transition: all 0.3s ease;
      &:hover {
        box-shadow: 0 12px 32px rgb(0 0 0 / 8%);
        transform: translateY(-2px);
      }
      .el-card__header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        .clearfix {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;

          // 通用按钮样式
          span {
            position: relative;
            padding-left: 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            &::before {
              position: absolute;
              top: 50%;
              left: 0;
              width: 4px;
              height: 16px;
              content: "";
              background: var(--el-color-primary);
              border-radius: 2px;
              transform: translateY(-50%);
            }
          }
          .el-button {
            padding: 8px 16px;
            margin-left: auto;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
              transform: translateY(-1px);
            }
            &.action-button {
              position: relative;
              padding: 10px 20px;
              overflow: hidden;
              font-size: 15px;
              font-weight: 600;
              letter-spacing: 0.5px;
              border-radius: 10px;
              box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.3);
              &::before {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                content: "";
                background: linear-gradient(
                  90deg,
                  rgb(255 255 255 / 0%) 0%,
                  rgb(255 255 255 / 20%) 50%,
                  rgb(255 255 255 / 0%) 100%
                );
                transition: all 0.8s ease;
              }
              &:hover {
                box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.5);
                transform: translateY(-3px);
                &::before {
                  left: 100%;
                }
              }
              .el-icon {
                margin-right: 6px;
                font-size: 16px;
                transition: all 0.3s ease;
              }
              &:hover .el-icon {
                transform: scale(1.2);
              }

              // 暗色模式适配
              .dark & {
                box-shadow: 0 6px 16px rgba(var(--el-color-primary-rgb), 0.2);
                &::before {
                  background: linear-gradient(90deg, rgb(0 0 0 / 0%) 0%, rgb(255 255 255 / 10%) 50%, rgb(0 0 0 / 0%) 100%);
                }
                &:hover {
                  box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.3);
                }
              }
            }
            &.refresh-button {
              margin-right: 10px;
            }
            &.add-button {
              // 添加按钮特有样式
            }
          }
        }
      }
      .el-card__body {
        padding: 24px;
        .el-container {
          .el-aside {
            overflow: hidden;
            .card {
              &.content-box {
                height: calc(100% - 48px); /* 减去两侧的padding值 */
                padding: 24px;
                background: var(--el-bg-color-overlay);
                border-radius: 16px;
                box-shadow: 0 4px 12px rgb(0 0 0 / 3%);
                transition: all 0.3s ease;
                &:hover {
                  box-shadow: 0 6px 16px rgb(0 0 0 / 5%);
                }
              }
            }
          }
          .el-main {
            padding: 0 20px;
            .card {
              margin-bottom: 20px;
              .clearfix {
                display: flex;
                align-items: center;
                justify-content: space-between;
                span {
                  position: relative;
                  padding-left: 12px;
                  font-size: 15px;
                  font-weight: 600;
                  color: var(--el-text-color-primary);
                  &::before {
                    position: absolute;
                    top: 50%;
                    left: 0;
                    width: 4px;
                    height: 16px;
                    content: "";
                    background: var(--el-color-primary);
                    border-radius: 2px;
                    transform: translateY(-50%);
                  }
                }
              }
            }
          }
        }
      }
    }

    // 图例样式
    .legend {
      display: flex;
      gap: 20px;
      justify-content: center;
      padding: 12px 16px;
      margin-top: 20px;
      background: var(--el-bg-color-overlay);
      border-radius: 8px;
      .legend-item {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 4px 12px;
        border-radius: 16px;
        transition: all 0.3s ease;
        &:hover {
          background: var(--el-color-primary-light-9);
        }
        .legend-line {
          width: 30px;
          height: 3px;
          border-radius: 2px;
        }
        span {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

/* 移动端适配 */
@media screen and (width <= 768px) {
  .main-box {
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    .filter {
      width: 100%;
      min-width: unset;
      max-height: 180px;
      padding: 8px;
      .title {
        margin-bottom: 6px;
        font-size: 14px;
      }
    }
    .main-content {
      width: 100%;
      .box-card {
        .el-card__header {
          padding: 12px 16px;
          .clearfix {
            flex-wrap: wrap;
            gap: 8px;
            span {
              font-size: 14px;
              &::before {
                width: 3px;
                height: 12px;
              }
            }
            .el-button {
              padding: 6px 12px;
              font-size: 13px;
              &.action-button {
                padding: 8px 16px;
                font-size: 14px;
                .el-icon {
                  font-size: 14px;
                }
              }
              &.refresh-button {
                margin-right: 6px;
              }
            }
          }
        }
        .el-card__body {
          padding: 12px;
          .el-container {
            flex-direction: column;
            height: auto !important;
            .el-aside {
              width: 100% !important;
              margin-bottom: 8px;
              .card.content-box {
                height: 350px;
                padding: 8px;
              }
            }
            .el-main {
              width: 100%;
              padding: 0;
              .device-list-header {
                height: 36px;
                padding: 0 8px;
                margin-bottom: 6px;
                .title-section .font-bold {
                  font-size: 14px;
                  &::before {
                    width: 3px;
                    height: 12px;
                  }
                }
              }
              .device-list-container {
                max-height: 350px;
                padding: 0 2px;
                .box-card {
                  margin-bottom: 6px;
                  .card-content {
                    padding: 8px 10px;
                    margin-bottom: 8px;
                    strong {
                      font-size: 14px;
                    }
                    .el-icon {
                      padding: 4px;
                    }
                  }
                  .el-row {
                    padding: 4px 10px;
                    margin-bottom: 4px;
                    .el-text {
                      font-size: 12px;
                      line-height: 1.4;
                      strong {
                        margin-right: 6px;
                        font-size: 12px;
                        &::before {
                          width: 2px;
                          height: 10px;
                        }
                      }
                    }
                  }
                  .el-row:last-child {
                    margin-bottom: 0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /* 图例移动端优化 */
  .legend {
    flex-wrap: wrap;
    gap: 8px !important;
    padding: 8px 12px !important;
    margin-top: 12px !important;
    .legend-item {
      padding: 3px 8px !important;
      .legend-line {
        width: 20px !important;
        height: 2px !important;
      }
      span {
        font-size: 12px !important;
      }
    }
  }
}

@media screen and (width <= 480px) {
  .main-box {
    gap: 6px;
    padding: 6px;
    .filter {
      max-height: 150px;
      padding: 6px;
      .title {
        margin-bottom: 4px;
        font-size: 13px;
      }
    }
    .main-content {
      .box-card {
        .el-card__header {
          padding: 8px 12px;
          .clearfix {
            flex-direction: column;
            gap: 6px;
            align-items: stretch;
            .el-button {
              width: 100%;
              margin-left: 0;
              &.refresh-button {
                margin-right: 0;
                margin-bottom: 6px;
              }
            }
          }
        }
        .el-card__body {
          padding: 8px;
          .el-container {
            .el-aside {
              .card.content-box {
                height: 280px;
                padding: 6px;
              }
            }
            .el-main {
              .device-list-header {
                height: 32px;
                padding: 0 6px;
                .title-section .font-bold {
                  font-size: 13px;
                }
              }
              .device-list-container {
                max-height: 280px;
                .box-card {
                  margin-bottom: 4px;
                  .card-content {
                    padding: 6px 8px;
                    margin-bottom: 6px;
                    strong {
                      font-size: 13px;
                    }
                  }
                  .el-row {
                    padding: 3px 8px;
                    margin-bottom: 3px;
                    .el-text {
                      font-size: 11px;
                      line-height: 1.3;
                      strong {
                        margin-right: 4px;
                        font-size: 11px;
                      }
                    }
                  }
                  .el-row:last-child {
                    margin-bottom: 0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 对话框移动端优化 */
@media screen and (width <= 768px) {
  .custom-dialog {
    :deep(.el-dialog) {
      width: 88vw !important;
      margin: 5vh auto !important;
      border-radius: 12px;
      .el-dialog__header {
        padding: 12px 16px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        .el-dialog__title {
          font-size: 16px;
          font-weight: 600;
        }
      }
      .el-dialog__body {
        max-height: 65vh;
        padding: 16px;
        overflow-y: auto;
      }
    }
    .dialog-container {
      .dialog-header {
        padding: 8px 0;
        margin-bottom: 12px !important;
        border-bottom: 1px solid var(--el-border-color-lighter);
        .el-checkbox {
          font-size: 14px;
          font-weight: 500;
        }
      }
      .dialog-body {
        gap: 8px !important;
        height: 280px !important;
        .device-card {
          width: calc(50% - 5px) !important;
          margin: 0 !important;
          border-radius: 8px;
          transition: all 0.3s ease;
          .device-info {
            padding: 12px;
            .el-row {
              .el-col {
                &:first-child {
                  span: 24;
                  text-align: center;
                }
                &:last-child {
                  span: 24;
                  margin-top: 8px;
                }
              }
            }
            .device-img {
              width: 36px !important;
              height: 36px !important;
              margin-bottom: 8px !important;
              border-radius: 6px;
            }
            .device-type {
              margin-bottom: 6px;
              font-size: 13px;
              font-weight: 500;
              color: var(--el-text-color-primary);
            }
            .device-name {
              font-size: 12px;
              .el-text strong {
                font-size: 12px;
                color: var(--el-text-color-primary);
              }
            }
            .device-mac {
              margin-top: 4px;
              font-size: 11px;
              .el-text {
                font-size: 11px;
                color: var(--el-text-color-regular);
              }
            }
          }
        }
      }
      .dialog-footer {
        padding: 12px 0 !important;
        border-top: 1px solid var(--el-border-color-lighter);
        .el-button {
          padding: 8px 16px;
          font-size: 14px;
          font-weight: 500;
          border-radius: 8px;
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  .custom-dialog {
    :deep(.el-dialog) {
      width: 94vw !important;
      margin: 3vh auto !important;
      .el-dialog__header {
        padding: 10px 12px;
        .el-dialog__title {
          font-size: 15px;
        }
      }
      .el-dialog__body {
        max-height: 75vh;
        padding: 12px;
      }
    }
    .dialog-container {
      .dialog-header {
        padding: 6px 0;
        margin-bottom: 10px !important;
      }
      .dialog-body {
        gap: 8px !important;
        height: 280px !important;
        .device-card {
          width: 100% !important;
          margin: 2px 0 !important;
          .device-info {
            .el-row {
              .el-col {
                &:first-child {
                  span: 8;
                }
                &:last-child {
                  span: 16;
                  margin-top: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 配置对话框移动端优化 */
@media screen and (width <= 768px) {
  :deep(.el-dialog) {
    &:not(.custom-dialog) {
      width: 85vw !important;
      margin: 5vh auto !important;
      .el-dialog__header {
        padding: 12px 16px;
        .el-dialog__title {
          font-size: 16px;
        }
      }
      .el-dialog__body {
        max-height: 65vh;
        padding: 12px 16px;
        overflow-y: auto;
        .el-form {
          .el-form-item {
            margin-bottom: 16px;
            .el-form-item__label {
              padding-bottom: 4px;
              font-size: 14px;
              line-height: 1.4;
            }
            .el-form-item__content {
              .el-switch {
                .el-switch__label {
                  font-size: 12px;
                }
              }
              .el-input {
                .el-input__inner {
                  font-size: 14px;
                }
              }
              .el-button {
                padding: 6px 12px;
                font-size: 13px;
                &.is-circle {
                  width: 28px;
                  height: 28px;
                  .el-icon {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  :deep(.el-dialog) {
    &:not(.custom-dialog) {
      width: 95vw !important;
      margin: 5vh auto !important;
      .el-dialog__header {
        padding: 12px 16px;
        .el-dialog__title {
          font-size: 15px;
        }
      }
      .el-dialog__body {
        padding: 16px;
      }
    }
    :deep(.el-form) {
      .el-form-item {
        margin-bottom: 16px;
        .el-form-item__label {
          padding-bottom: 6px;
          font-size: 13px;
        }
        .el-form-item__content {
          .el-input {
            .el-input__inner {
              height: 36px;
              font-size: 13px;
            }
          }
        }
      }
    }
    .dialog-container {
      .dialog-footer {
        .el-button {
          width: 45%;
          padding: 8px 12px;
          margin: 0 2.5%;
          font-size: 13px;
        }
      }
    }
  }
}

/* 设备信息卡片移动端优化 */
@media screen and (width <= 768px) {
  .device-card {
    .device-info {
      height: auto !important;
      .device-name {
        .el-text {
          strong {
            display: block;
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}

/* 超小屏幕优化 (≤320px) */
@media screen and (width <= 320px) {
  .main-box {
    gap: 4px;
    padding: 4px;
    .main-content {
      .box-card {
        .el-card__body {
          padding: 6px;
          .el-container {
            .el-main {
              .device-list-header {
                height: 28px;
                padding: 0 4px;
                margin-bottom: 4px;
              }
              .device-list-container {
                padding: 0 1px;
                .box-card {
                  margin-bottom: 3px;
                  .card-content {
                    padding: 4px 6px;
                    margin-bottom: 4px;
                    strong {
                      font-size: 12px;
                    }
                  }
                  .el-row {
                    padding: 2px 6px;
                    margin-bottom: 2px;
                    .el-text {
                      font-size: 10px;
                      line-height: 1.2;
                      strong {
                        margin-right: 3px;
                        font-size: 10px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 旧的添加远程网络弹窗样式已被新样式替代 */

/* 旧的小屏添加弹窗样式已被新样式替代 */

@media screen and (width <= 480px) {
  .config-rnet-dialog {
    :deep(.el-dialog) {
      width: 98vw !important;
      max-height: 98vh;
      margin: 1vh auto !important;
      .el-dialog__header {
        padding: 10px 12px;
        .el-dialog__title {
          font-size: 15px;
        }
      }
      .el-dialog__body {
        max-height: 85vh;
        padding: 12px;
      }
    }
    :deep(.el-form) {
      .el-form-item {
        margin-bottom: 20px;
        .el-form-item__label {
          padding-bottom: 8px;
          font-size: 14px;
          font-weight: 600;
        }
        .el-form-item__content {
          // 开关组件小屏优化
          .el-switch {
            margin: 10px 0;
            .el-switch__core {
              width: 45px !important;
              height: 22px !important;
              margin-right: 10px;
            }
            .el-switch__label {
              margin: 0 6px;
              font-size: 13px !important;
              line-height: 22px;
              &.el-switch__label--left {
                margin-right: 10px;
              }
              &.el-switch__label--right {
                margin-left: 10px;
              }
            }
          }
          .el-input {
            margin-bottom: 8px;
            .el-input__inner {
              height: 38px;
              padding: 0 10px;
              font-size: 13px;
            }
            .el-input__inner::placeholder {
              font-size: 12px;
            }
          }
          .el-button {
            padding: 8px 12px;
            font-size: 13px;
            &.is-circle {
              width: 34px;
              height: 34px;
              .el-icon {
                font-size: 14px;
              }
            }
          }

          /* IP输入行的布局 */
          > div {
            gap: 8px;
            margin-bottom: 12px;
          }
        }
      }

      /* 最后一个表单项（按钮组） */
      .el-form-item:last-child {
        padding-top: 20px;
        margin-top: 25px;
        .el-form-item__content {
          flex-direction: column;
          gap: 10px;
          .el-button {
            flex: none;
            width: 100%;
            max-width: none;
            padding: 10px 20px;
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

/* 旧的超小屏添加弹窗样式已被新样式替代 */

/* 配置远程网络设备弹窗移动端优化 */
@media screen and (width <= 768px) {
  .config-rnet-dialog {
    :deep(.el-dialog) {
      width: 95vw !important;
      max-width: 500px;
      max-height: 90vh;
      margin: 5vh auto !important;
      border-radius: 12px;
      box-shadow: 0 12px 32px rgb(0 0 0 / 15%);
      .el-dialog__header {
        padding: 16px 20px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        .el-dialog__title {
          font-size: 18px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }
      .el-dialog__body {
        max-height: 70vh;
        padding: 20px;
        overflow-y: auto;
      }
    }

    // 响应式表单布局优化
    :deep(.el-form) {
      .el-form-item {
        display: block !important;
        margin-bottom: 28px;
        .el-form-item__label {
          display: block !important;
          width: 100% !important;
          min-width: auto !important;
          max-width: none !important;
          padding-right: 0 !important;
          padding-bottom: 12px;
          margin-bottom: 0;
          overflow: visible !important;
          font-size: 15px;
          font-weight: 600;
          line-height: 1.4;
          color: var(--el-text-color-primary);
          text-align: left !important;
          word-break: break-word;
          white-space: normal !important;
        }
        .el-form-item__content {
          width: 100% !important;
          margin-left: 0 !important;

          // 开关组件移动端优化
          .el-switch {
            display: flex !important;
            align-items: center;
            width: 100%;
            margin: 12px 0;
            .el-switch__core {
              order: 2;
              width: 50px !important;
              height: 24px !important;
              margin-right: 12px;
            }
            .el-switch__label {
              font-size: 14px !important;
              font-weight: 500;
              line-height: 24px;
              color: var(--el-text-color-regular);
              white-space: nowrap;
              &.el-switch__label--left {
                order: 1;
                margin-right: 12px;
                margin-left: 0;
              }
              &.el-switch__label--right {
                order: 3;
                margin-right: 0;
                margin-left: 12px;
              }
            }
          }

          // IP输入框组合布局优化
          > div[style*="display: flex"] {
            display: flex !important;
            gap: 10px;
            align-items: flex-start;
            margin-bottom: 15px;
            .el-form-item {
              flex: 1;
              min-width: 0;
              margin-bottom: 0;
              .el-form-item__content {
                margin-left: 0 !important;
                .el-input {
                  width: 100%;
                  margin-bottom: 0;
                  .el-input__inner {
                    height: 42px;
                    padding: 0 12px;
                    font-size: 14px;
                    border: 1px solid var(--el-border-color);
                    border-radius: 8px;
                    transition: border-color 0.2s;
                    &:focus {
                      border-color: var(--el-color-primary);
                    }
                    &::placeholder {
                      font-size: 13px;
                      color: var(--el-text-color-placeholder);
                    }
                  }
                }
              }
            }
            .el-button.is-circle {
              flex-shrink: 0;
              width: 42px;
              height: 42px;
              margin-top: 0;
              border-radius: 50%;
              .el-icon {
                font-size: 16px;
              }
            }
          }

          // 单独的输入框（非组合的）
          .el-input:not([style*="flex"]) {
            width: 100%;
            margin-bottom: 10px;
            .el-input__inner {
              height: 42px;
              padding: 0 12px;
              font-size: 14px;
              border-radius: 8px;
            }
          }

          // 添加IP按钮优化
          .el-button.is-circle:not([style*="flex"]) {
            width: 42px;
            height: 42px;
            margin-top: 10px;
          }
        }
      }

      /* 按钮组优化 */
      .el-form-item:last-child {
        padding-top: 25px;
        margin-top: 30px;
        text-align: center;
        border-top: 1px solid var(--el-border-color-lighter);
        .el-form-item__content {
          display: flex;
          gap: 15px;
          justify-content: center;
          margin-left: 0 !important;
          .el-button {
            flex: 1;
            max-width: 140px;
            height: 44px;
            padding: 12px 24px;
            font-size: 15px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.2s;
            &[type="primary"],
            &.el-button--primary {
              background: var(--el-color-primary);
              border-color: var(--el-color-primary);
              &:hover {
                background: var(--el-color-primary-light-3);
                border-color: var(--el-color-primary-light-3);
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  .config-rnet-dialog {
    :deep(.el-dialog) {
      width: 98vw !important;
      max-height: 95vh;
      margin: 2.5vh auto !important;
      border-radius: 8px;
      .el-dialog__header {
        padding: 12px 16px;
        .el-dialog__title {
          font-size: 16px;
        }
      }
      .el-dialog__body {
        max-height: 75vh;
        padding: 16px;
      }
    }
    :deep(.el-form) {
      .el-form-item {
        margin-bottom: 24px;
        .el-form-item__label {
          padding-bottom: 10px;
          font-size: 14px;
        }
        .el-form-item__content {
          // 开关组件小屏优化
          .el-switch {
            margin: 8px 0;
            .el-switch__core {
              width: 45px !important;
              height: 22px !important;
            }
            .el-switch__label {
              font-size: 13px !important;
            }
          }

          // IP输入框组合小屏优化
          > div[style*="display: flex"] {
            gap: 8px;
            margin-bottom: 12px;
            .el-form-item {
              .el-form-item__content {
                .el-input {
                  .el-input__inner {
                    height: 38px;
                    padding: 0 10px;
                    font-size: 13px;
                  }
                }
              }
            }
            .el-button.is-circle {
              width: 38px;
              height: 38px;
              .el-icon {
                font-size: 14px;
              }
            }
          }

          // 添加IP按钮小屏优化
          .el-button.is-circle:not([style*="flex"]) {
            width: 38px;
            height: 38px;
            margin-top: 8px;
          }
        }
      }

      /* 按钮组小屏优化 */
      .el-form-item:last-child {
        padding-top: 20px;
        margin-top: 24px;
        .el-form-item__content {
          flex-direction: column;
          gap: 10px;
          .el-button {
            flex: none;
            width: 100%;
            max-width: none;
            height: 42px;
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

/* 树形节点内容样式 */
.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 4px;
  .tree-node-label {
    flex: 1;
    padding-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .tree-node-actions {
    flex-shrink: 0;
    opacity: 0;
    transition: all 0.2s ease;
    .tree-edit-btn {
      width: 20px;
      height: 20px;
      padding: 0;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      border: none;
      &:hover {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
        transform: scale(1.1);
      }
      &:active {
        transform: scale(1.05);
      }
    }
  }
  &:hover .tree-node-actions {
    opacity: 1;
  }
}

/* 移动端适配 */
@media screen and (width <= 768px) {
  .tree-node-content {
    .tree-node-actions {
      opacity: 1; // 移动端始终显示操作按钮
      .tree-edit-btn {
        width: 18px;
        height: 18px;
        font-size: 11px;
      }
    }
  }
}

/* 黑暗模式下的树形节点样式 */
html.dark .tree-node-content {
  .tree-edit-btn {
    color: var(--el-text-color-secondary);
    &:hover {
      color: var(--el-color-primary-light-3);
      background-color: var(--el-color-primary-dark-2);
    }
  }
}

/* 美化添加和编辑远程网络弹窗 */
.add-rnet-dialog,
.edit-rnet-dialog {
  :deep(.el-dialog) {
    border-radius: 20px;
    box-shadow: 0 15px 35px rgb(0 0 0 / 12%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    &:hover {
      box-shadow: 0 20px 40px rgb(0 0 0 / 15%);
      transform: translateY(-2px);
    }
    .el-dialog__header {
      position: relative;
      padding: 24px 30px 20px;
      margin: 0;
      background: var(--el-bg-color-overlay);
      border-bottom: 1px solid var(--el-border-color-lighter);
      border-radius: 20px 20px 0 0;
      &::after {
        position: absolute;
        bottom: -1px;
        left: 50%;
        width: 60px;
        height: 3px;
        content: "";
        background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-5));
        border-radius: 3px;
        transform: translateX(-50%);
      }
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        letter-spacing: 0.5px;
      }
    }
    .el-dialog__body {
      padding: 30px;
      background: var(--el-bg-color);
    }
  }
  .dialog-header-wrapper {
    display: flex;
    gap: 12px;
    align-items: center;
    .dialog-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
      border-radius: 50%;
      transition: all 0.3s ease;
      &:hover {
        background: var(--el-color-primary-light-8);
        transform: scale(1.1);
      }
    }
  }
  .dialog-container {
    .dialog-body {
      margin-bottom: 30px;
      .el-form {
        .el-form-item {
          margin-bottom: 24px;
          .el-form-item__label {
            margin-bottom: 8px;
            font-size: 15px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
          .el-input {
            .el-input__wrapper {
              border: 2px solid var(--el-border-color-light);
              border-radius: 12px;
              box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              &:hover {
                border-color: var(--el-color-primary-light-5);
                box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
              }
              &.is-focus {
                border-color: var(--el-color-primary);
                box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.2);
              }
              .el-input__inner {
                padding: 0 16px;
                font-size: 15px;
                &::placeholder {
                  font-size: 14px;
                  color: var(--el-text-color-placeholder);
                }
              }
              .el-input__prefix-inner {
                color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }
    .dialog-footer {
      display: flex;
      gap: 16px;
      justify-content: center;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
      .el-button {
        position: relative;
        min-width: 120px;
        padding: 12px 24px;
        overflow: hidden;
        font-size: 15px;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        &:not(.el-button--primary) {
          color: var(--el-text-color-regular);
          background: var(--el-bg-color);
          border: 2px solid var(--el-border-color);
          &:hover {
            color: var(--el-color-primary);
            background: var(--el-color-primary-light-9);
            border-color: var(--el-color-primary-light-5);
            box-shadow: 0 6px 16px rgb(0 0 0 / 10%);
            transform: translateY(-2px);
          }
        }
        &.el-button--primary {
          background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
          border: none;
          box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.3);
          &:hover {
            background: linear-gradient(135deg, var(--el-color-primary-light-3) 0%, var(--el-color-primary) 100%);
            box-shadow: 0 6px 20px rgba(var(--el-color-primary-rgb), 0.4);
            transform: translateY(-2px);
          }
          &:active {
            box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.3);
            transform: translateY(0);
          }
        }
        .mr-1 {
          margin-right: 6px;
        }
      }
    }
  }
}

/* 暗色模式下的弹窗样式 */
html.dark {
  .add-rnet-dialog,
  .edit-rnet-dialog {
    :deep(.el-dialog) {
      .el-dialog__header {
        background: var(--el-bg-color-overlay);
        &::after {
          background: linear-gradient(90deg, var(--el-color-primary-light-3), var(--el-color-primary-light-5));
        }
      }
      .el-dialog__body {
        background: var(--el-bg-color);
      }
    }
    .dialog-header-wrapper {
      .dialog-icon {
        color: var(--el-color-primary-light-3);
        background: var(--el-color-primary-dark-2);
        &:hover {
          color: var(--el-color-primary);
          background: var(--el-color-primary-light-8);
        }
      }
    }
    .dialog-container {
      .dialog-body {
        .el-form {
          .el-form-item {
            .el-input {
              .el-input__wrapper {
                background: var(--el-fill-color-blank);
                border-color: var(--el-border-color);
                &:hover {
                  border-color: var(--el-color-primary-light-5);
                }
                &.is-focus {
                  border-color: var(--el-color-primary-light-3);
                  box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.15);
                }
              }
            }
          }
        }
      }
      .dialog-footer {
        border-color: var(--el-border-color);
        .el-button {
          &:not(.el-button--primary) {
            background: var(--el-fill-color-blank);
            border-color: var(--el-border-color);
            &:hover {
              background: var(--el-color-primary-dark-2);
              border-color: var(--el-color-primary-light-5);
            }
          }
        }
      }
    }
  }
}

/* 移动端响应式优化 */
@media screen and (width <= 768px) {
  .add-rnet-dialog,
  .edit-rnet-dialog {
    :deep(.el-dialog) {
      width: 90vw !important;
      margin: 10vh auto !important;
      border-radius: 16px;
      .el-dialog__header {
        padding: 20px 24px 16px;
        .el-dialog__title {
          font-size: 16px;
        }
      }
      .el-dialog__body {
        padding: 24px;
      }
    }
    .dialog-header-wrapper {
      gap: 10px;
      .dialog-icon {
        width: 36px;
        height: 36px;
      }
    }
    .dialog-container {
      .dialog-body {
        margin-bottom: 24px;
        .el-form {
          .el-form-item {
            margin-bottom: 20px;
            .el-form-item__label {
              font-size: 14px;
            }
            .el-input {
              .el-input__wrapper {
                .el-input__inner {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
      .dialog-footer {
        gap: 12px;
        .el-button {
          min-width: 100px;
          padding: 10px 20px;
          font-size: 14px;
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  .add-rnet-dialog,
  .edit-rnet-dialog {
    :deep(.el-dialog) {
      width: 95vw !important;
      margin: 5vh auto !important;
      border-radius: 12px;
      .el-dialog__header {
        padding: 16px 20px 12px;
        .el-dialog__title {
          font-size: 15px;
        }
      }
      .el-dialog__body {
        padding: 20px;
      }
    }
    .dialog-header-wrapper {
      gap: 8px;
      .dialog-icon {
        width: 32px;
        height: 32px;
      }
    }
    .dialog-container {
      .dialog-body {
        margin-bottom: 20px;
        .el-form {
          .el-form-item {
            margin-bottom: 16px;
            .el-form-item__label {
              font-size: 13px;
            }
            .el-input {
              .el-input__wrapper {
                border-radius: 10px;
                .el-input__inner {
                  padding: 0 14px;
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
      .dialog-footer {
        flex-direction: column;
        gap: 10px;
        .el-button {
          width: 100%;
          min-width: auto;
          padding: 12px 20px;
          font-size: 14px;
        }
      }
    }
  }
}
