<template>
  <div>
    <div v-if="portSettingsLoading" style="padding: 16px">
      <el-skeleton :rows="8" animated />
    </div>
    <div v-else>
      <el-divider v-if="deviceConfig.system.swPort && drawerProps.row?.deviceType === 'switch'"></el-divider>
      <el-card
        v-if="drawerProps.row?.deviceType === 'switch' && deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0"
      >
        <div class="sw-port-container">
          <!-- 端口数 <= 5 时，单行显示 -->
          <ul v-if="deviceConfig.system.swPort.length <= 5" class="sw-port-list single-row">
            <li
              v-for="(item, index) in deviceConfig.system.swPort"
              :key="index"
              :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
              @click="toggleRowSelection(item, portTableRef)"
            >
              <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
              <div class="port-name-container">
                <el-input
                  v-if="item.isEditing"
                  v-model="item.describe"
                  size="small"
                  ref="portNameInput"
                  @blur="handleDescribeConfirm(item)"
                  @keyup.enter="handleDescribeConfirm(item)"
                  @keyup.esc="handleDescribeCancel(item)"
                  maxlength="32"
                  show-word-limit
                  class="port-name-input"
                ></el-input>
                <div v-else class="port-name-display" @click.stop="handleDescribeEdit(item)">
                  <el-text class="port-name-text" :title="item.describe || item.name">{{ item.describe || item.name }}</el-text>
                  <el-icon class="edit-icon"><Edit /></el-icon>
                </div>
              </div>
            </li>
          </ul>

          <!-- 端口数 > 5 时，两行显示：GE1 GE3 GE5 / GE2 GE4 GE6 -->
          <div v-else class="sw-port-multi-row">
            <!-- 第一行：奇数索引端口 -->
            <ul class="sw-port-row">
              <li
                v-for="(item, index) in deviceConfig.system.swPort.filter((_, i) => i % 2 === 0)"
                :key="'odd-' + index"
                :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
                @click="toggleRowSelection(item, portTableRef)"
              >
                <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                <div class="port-name-container">
                  <el-input
                    v-if="item.isEditing"
                    v-model="item.describe"
                    size="small"
                    ref="portNameInput"
                    @blur="handleDescribeConfirm(item)"
                    @keyup.enter="handleDescribeConfirm(item)"
                    @keyup.esc="handleDescribeCancel(item)"
                    maxlength="32"
                    show-word-limit
                    class="port-name-input"
                  ></el-input>
                  <div v-else class="port-name-display" @click.stop="handleDescribeEdit(item)">
                    <el-text class="port-name-text" :title="item.describe || item.name">{{ item.describe || item.name }}</el-text>
                    <el-icon class="edit-icon"><Edit /></el-icon>
                  </div>
                </div>
              </li>
            </ul>

            <!-- 第二行：偶数索引端口 -->
            <ul class="sw-port-row">
              <li
                v-for="(item, index) in deviceConfig.system.swPort.filter((_, i) => i % 2 === 1)"
                :key="'even-' + index"
                :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
                @click="toggleRowSelection(item, portTableRef)"
              >
                <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                <div class="port-name-container">
                  <el-input
                    v-if="item.isEditing"
                    v-model="item.describe"
                    size="small"
                    ref="portNameInput"
                    @blur="handleDescribeConfirm(item)"
                    @keyup.enter="handleDescribeConfirm(item)"
                    @keyup.esc="handleDescribeCancel(item)"
                    maxlength="32"
                    show-word-limit
                    class="port-name-input"
                  ></el-input>
                  <div v-else class="port-name-display" @click.stop="handleDescribeEdit(item)">
                    <el-text class="port-name-text" :title="item.describe || item.name">{{ item.describe || item.name }}</el-text>
                    <el-icon class="edit-icon"><Edit /></el-icon>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <el-button link :icon="showPortExample ? 'ArrowUp' : 'ArrowDown'" @click="togglePortExample">{{
          showPortExample ? t("common.CollapseDiagram") : t("common.ExpandDiagram")
        }}</el-button>
        <ul class="sw-port-list" v-if="showPortExample">
          <li v-for="(item, index) in portStates" :key="index" class="sw-port-example">
            <el-image :src="item.icon" class="port-icon" :alt="item.text" />
            <span class="port-text">{{ item.text }}</span>
          </li>
        </ul>
      </el-card>
      <el-divider></el-divider>
      <el-button size="default" type="primary" @click="openPortDialog">{{ t("device.configuration") }}</el-button>
      <el-table
        ref="portTableRef"
        :data="deviceConfig.system.swPort"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :selection="selectedRows"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column :label="$t('device.portName')">
          <template #default="scope">
            <el-input
              v-if="scope.row.isEditing"
              v-model="scope.row.describe"
              ref="tablePortNameInput"
              @blur="handleDescribeConfirm(scope.row)"
              @keyup.enter="handleDescribeConfirm(scope.row)"
              @keyup.esc="handleDescribeCancel(scope.row)"
              maxlength="32"
              show-word-limit
            >
            </el-input>
            <div v-else class="edit-cell" @click="handleDescribeEdit(scope.row)">
              {{ scope.row.describe || scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="portenable" :label="t('device.portStatus')">
          <template #default="scope">
            <span>{{ scope.row.portenable === 1 ? $t("device.open") : $t("device.close") }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('device.portPowerLimit')" width="180px">
          <template #default="scope">
            <span>{{ deviceConfig.system?.swPoe[scope.$index]?.power ?? "--" }}/</span>
            {{
              deviceConfig.system.swPoe[scope.$index]?.powerout === 0
                ? "af (15.4w)"
                : deviceConfig.system.swPoe[scope.$index]?.powerout === 1
                  ? "at (30w)"
                  : "--"
            }}
          </template>
        </el-table-column>
        <el-table-column prop="power" :label="$t('device.portRate')">
          <template #default="scope">
            {{ scope.row.link === 1 ? formatSpeedDuplex(scope.row.speed_duplex) : formatAutoneg(scope.row.autoneg) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog v-model="portDialogVisible" :title="$t('device.portSettings')" width="700px" draggable>
      <el-text style="margin: 10px; font-size: 16px; font-weight: bolder; text-align: center">
        {{ t("device.configApplyTip") }}
      </el-text>
      {{ selectedRowNames }}
      <el-card v-if="deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0">
        <el-tabs tab-position="left" style="height: 300px" class="demo-tabs" v-model="dialogTabActive">
          <el-tab-pane :label="$t('device.portSettings')" name="first">
            <el-form ref="portForm" :model="swPort" label-width="160px" :hide-required-asterisk="true">
              <el-form-item :label="$t('device.portSwitch')">
                <el-switch v-model="swPort.portenable" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.portExtend')">
                <el-switch v-model="swPort.extend" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.adaptive')" v-if="swPort.extend === 0">
                <el-switch v-model="swPort.autoneg" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.flowControl')" v-if="swPort.autoneg == 0 && swPort.extend === 0">
                <el-switch v-model="swPort.flwctrl" :inactive-value="0" :active-value="1" />
              </el-form-item>
              <el-form-item :label="$t('device.speedDuplex')" v-if="swPort.autoneg == 0 && swPort.extend === 0">
                <el-select v-model="swPort.speed_duplex" :placeholder="t('common.pleaseSelect')">
                  <el-option
                    v-for="speedOption in speedDuplexOptions"
                    :key="speedOption.value"
                    :label="formatSpeedDuplex(speedOption.label)"
                    :value="speedOption.value"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <!-- POE配置 -->
          <el-tab-pane :label="$t('device.PoeSettings')" name="second">
            <el-form ref="poeForm" :model="swPoe" label-width="100px" :hide-required-asterisk="true">
              <el-form-item :label="$t('device.poeSwitch')">
                <el-switch v-model="swPoe.poeenable" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.poeClass')">
                <el-text>{{ swPoe.poeclass }}</el-text>
              </el-form-item>
              <el-form-item :label="$t('device.portPowerLimit')">
                <el-select v-model="swPoe.powerout" :placeholder="t('common.pleaseSelect')">
                  <el-option v-for="(label, value) in poePowerOptions" :key="value" :label="label" :value="Number(value)" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('device.portPower')">
                <el-text>{{ (swPoe.power || 0) + " W" }}</el-text>
              </el-form-item>
              <el-form-item :label="$t('device.poeWatchDog')">
                <el-switch v-model="swPoe.poewd" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.PoeWatchDogTime')" v-if="swPoe.poewd === 1">
                <el-input-number v-model="swPoe.poetime" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('device.VlanSettings')" name="third">
            <el-form ref="vlanForm" :model="swVlan" :rules="vlanRules" label-width="100px" :hide-required-asterisk="true">
            </el-form>
          </el-tab-pane>
          <!-- 风暴抑制 -->
          <el-tab-pane :label="$t('device.StormSetting')" name="fourth">
            <el-form ref="stormForm" :model="swStorm" label-width="100px" :hide-required-asterisk="true">
              <el-form-item :label="$t('device.trafficType')">
                <el-checkbox-group v-model="selectedTrafficTypes" @change="updateTrafficType">
                  <el-checkbox
                    v-for="trafficOption in trafficTypeOptionsArray"
                    :key="trafficOption.value"
                    :value="trafficOption.value"
                  >
                    {{ trafficOption.description }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item :label="t('common.unicast')" v-if="selectedTrafficTypes.includes(4)">
                <el-tree-select
                  v-model="swStorm.rate3"
                  :data="rateOptions"
                  :props="{
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    checkStrictly: true,
                    disabled: data =>
                      data.value === 'common' ||
                      data.value === 'custom' ||
                      (typeof data.value === 'string' && data.value.startsWith('range-')),
                    isLeaf: data => !data.children || data.children.length === 0,
                    hasChildren: data =>
                      data.value === 'custom' || (typeof data.value === 'string' && data.value.startsWith('range-'))
                  }"
                  check-strictly
                  node-key="value"
                  :expand-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-form-item>

              <el-form-item :label="t('common.multicast')" v-if="selectedTrafficTypes.includes(1)">
                <el-tree-select
                  v-model="swStorm.rate1"
                  :data="rateOptions"
                  :props="{
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    checkStrictly: true,
                    disabled: data =>
                      data.value === 'common' ||
                      data.value === 'custom' ||
                      (typeof data.value === 'string' && data.value.startsWith('range-')),
                    isLeaf: data => !data.children || data.children.length === 0,
                    hasChildren: data =>
                      data.value === 'custom' || (typeof data.value === 'string' && data.value.startsWith('range-'))
                  }"
                  check-strictly
                  node-key="value"
                  :expand-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-form-item>

              <el-form-item :label="t('common.broadcast')" v-if="selectedTrafficTypes.includes(2)">
                <el-tree-select
                  v-model="swStorm.rate2"
                  :data="rateOptions"
                  :props="{
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    checkStrictly: true,
                    disabled: data =>
                      data.value === 'common' ||
                      data.value === 'custom' ||
                      (typeof data.value === 'string' && data.value.startsWith('range-')),
                    isLeaf: data => !data.children || data.children.length === 0,
                    hasChildren: data =>
                      data.value === 'custom' || (typeof data.value === 'string' && data.value.startsWith('range-'))
                  }"
                  check-strictly
                  node-key="value"
                  :expand-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('device.IsolationControl')" name="fifth">
            <el-form ref="isolationForm" :model="swPoe" label-width="100px" :hide-required-asterisk="true">
              <el-card v-if="deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0">
                <div class="sw-port-container">
                  <!-- 端口数 <= 5 时，单行显示 -->
                  <ul v-if="deviceConfig.system.swPort.length <= 5" class="sw-port-list single-row">
                    <li
                      v-for="item in deviceConfig.system.swPort"
                      :key="item.port"
                      :class="['sw-port-tag', { 'selected-port': isPortSelected(item) }]"
                      @click="togglePortSelection(item)"
                    >
                      <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                      <div class="port-name-container">
                        <div class="port-name-display">
                          <el-text class="port-name-text" :title="item.name">{{ item.name }}</el-text>
                        </div>
                      </div>
                    </li>
                  </ul>

                  <!-- 端口数 > 5 时，两行显示 -->
                  <div v-else class="sw-port-multi-row">
                    <!-- 第一行：奇数索引端口 -->
                    <ul class="sw-port-row">
                      <li
                        v-for="item in deviceConfig.system.swPort.filter((_, i) => i % 2 === 0)"
                        :key="'odd-' + item.port"
                        :class="['sw-port-tag', { 'selected-port': isPortSelected(item) }]"
                        @click="togglePortSelection(item)"
                      >
                        <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                        <div class="port-name-container">
                          <div class="port-name-display">
                            <el-text class="port-name-text" :title="item.name">{{ item.name }}</el-text>
                          </div>
                        </div>
                      </li>
                    </ul>

                    <!-- 第二行：偶数索引端口 -->
                    <ul class="sw-port-row">
                      <li
                        v-for="item in deviceConfig.system.swPort.filter((_, i) => i % 2 === 1)"
                        :key="'even-' + item.port"
                        :class="['sw-port-tag', { 'selected-port': isPortSelected(item) }]"
                        @click="togglePortSelection(item)"
                      >
                        <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                        <div class="port-name-container">
                          <div class="port-name-display">
                            <el-text class="port-name-text" :title="item.name">{{ item.name }}</el-text>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
                <el-button link @click="isolateAll">{{ t("common.selectAll") }}</el-button>
                <el-divider></el-divider>
                <el-text>
                  {{ t("common.selectedPorts") }}:
                  <!-- 如果 isolateRows 为空，显示 swIsolate，否则显示 isolateRows 的内容 -->
                  <span v-if="isolateRows.length === 0">
                    <!-- 根据 swIsolate 数组的数字，匹配 deviceConfig.system.swPort 的 port 字段，显示对应的 name 字段 -->
                    {{ getPortNames(swIsolate).join(", ") }}
                  </span>
                  <span v-else>
                    {{ isolateRows.map(item => item.name).join(", ") }}
                  </span>
                </el-text>
              </el-card>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('device.QosSettings')" name="sixth">
            <el-form ref="qosForm" :model="swQos" label-width="100px" :hide-required-asterisk="true">
              <el-form-item :label="$t('device.qosTip')">
                <el-select v-model="swQos.qos" :placeholder="t('common.pleaseSelect')">
                  <!-- 动态生成 0-7 的选项 -->
                  <el-option v-for="value in 8" :key="value" :label="`QoS ${value - 1}`" :value="value - 1" />
                </el-select>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-card>
      <template #footer>
        <el-button @click="portDialogVisible = false">{{ t("common.cancel") }}</el-button>
        <el-button type="primary" :disabled="!hasDialogConfigChanged" @click="() => handleSubmit(true)">{{
          t("common.confirm")
        }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="PortSettings">
// Removed unused imports

import { onMounted, ref, computed, nextTick } from "vue";
import { Edit } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import {
  deviceConfig,
  drawerProps,
  swPort,
  swPoe,
  swVlan,
  swStorm,
  swIsolate,
  swQos,
  selectedRows,
  selectedRowNames,
  portDialogVisible,
  dialogTabActive,
  handleSelectionChange,
  isRowSelected,
  toggleRowSelection,
  formatAutoneg,
  submitConfig,
  generatePortData,
  speedDuplexOptions,
  poePowerOptions,
  rateOptions,
  updateTrafficType,
  trafficTypeOptionsArray,
  getPortNames
} from "@/api/interface/deviceConfigDrawer";
import { formatSpeedDuplex } from "@/api/interface/device/formatter";
import portActiveIcon from "@/assets/images/port_active_icon.png";
import portDeactiveIcon from "@/assets/images/port_deactive_icon.png";
import { isolateAll, isolateRows, isPortSelected, togglePortSelection } from "@/api/interface/apGroupDrawer";

const { t } = useI18n();

const portSettingsLoading = ref(false);
const showPortExample = ref(false);
const portTableRef = ref();
const selectedTrafficTypes = ref([]);

// VLAN 表单验证规则
const vlanRules = ref({});

// 端口状态示例数据
const portStates = computed(() => [
  { icon: portActiveIcon, text: `${t("common.connected")} | ${t("common.powerOff")}` },
  { icon: portDeactiveIcon, text: `${t("common.disconnected")} | ${t("common.powerOff")}` }
]);

// 切换端口示例显示
const togglePortExample = () => {
  showPortExample.value = !showPortExample.value;
};

// 打开端口配置对话框
const openPortDialog = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning(t("device.selectPortTip"));
    return;
  }
  portDialogVisible.value = true;
};

// 处理端口描述编辑
const handleDescribeEdit = (row: any) => {
  console.log("开始编辑端口描述:", row);
  // 保存原始值，用于取消时恢复
  row.originalDescribe = row.describe;
  row.isEditing = true;

  // 使用 nextTick 确保输入框渲染后再聚焦
  nextTick(() => {
    const inputs = document.querySelectorAll('input[maxlength="32"]');
    const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
    if (lastInput) {
      lastInput.focus();
      lastInput.select();
    }
  });
};

const handleDescribeCancel = (row: any) => {
  console.log("取消端口描述编辑:", row);
  // 恢复原始值
  if (row.originalDescribe !== undefined) {
    row.describe = row.originalDescribe;
    delete row.originalDescribe;
  }
  row.isEditing = false;
};

const handleDescribeConfirm = async (row: any) => {
  console.log("确认端口描述修改:", row);
  row.isEditing = false;

  try {
    await modifyDescribe(row);
    console.log("端口描述修改成功");
    // 清理原始值
    delete row.originalDescribe;
  } catch (error) {
    console.error("端口描述修改失败:", error);
    ElMessage.error("端口描述修改失败");
    // 如果修改失败，恢复原始值
    if (row.originalDescribe !== undefined) {
      row.describe = row.originalDescribe;
      delete row.originalDescribe;
    }
  }
};

// 字符串化对象，移除空值
const stringifyWithoutEmpty = (obj: any) => {
  const cleanObj = Object.fromEntries(
    Object.entries(obj).filter(([, value]) => value !== null && value !== undefined && value !== "")
  );
  return JSON.stringify(cleanObj);
};

// 修改端口描述
const modifyDescribe = async (row: any) => {
  console.log("修改描述:", stringifyWithoutEmpty(row));

  // 验证描述长度（最大32个字节）
  if (row.describe && new Blob([row.describe]).size > 32) {
    ElMessage.error("端口描述最大长度为32个字节");
    throw new Error("端口描述长度超限");
  }

  // 构造提交数据，只包含必要的字段
  const portData = {
    name: row.name,
    port: row.port,
    describe: row.describe || row.name, // 如果描述为空，使用端口名称
    portenable: row.portenable,
    autoneg: row.autoneg,
    extend: row.extend,
    speed_duplex: row.speed_duplex,
    flwctrl: row.flwctrl
  };

  console.log("提交端口描述数据:", portData);
  await submitConfig({ system: { swPort: [portData] } });
};

// 处理节点点击
const handleNodeClick = (data: any) => {
  console.log("Node clicked:", data);
};

// 检查对话框配置是否有变化
const hasDialogConfigChanged = computed(() => {
  // 如果对话框没有打开，返回 false
  if (!portDialogVisible.value) {
    return false;
  }

  // 如果没有选中端口，返回 false
  if (!selectedRows.value || selectedRows.value.length === 0) {
    return false;
  }

  // 简化实现：如果有选中的端口，就认为可能有变化
  return selectedRows.value.length > 0;
});

// 处理提交
const handleSubmit = async (isDialog = false) => {
  if (isDialog) {
    try {
      // 构造提交数据
      let submitData = {};

      switch (dialogTabActive.value) {
        case "first":
          submitData = { system: { swPort: generatePortData(swPort.value) } };
          break;
        case "second":
          submitData = { system: { swPoe: generatePortData(swPoe.value) } };
          break;
        case "third":
          submitData = { network: { swVlan: generatePortData(swVlan.value) } };
          break;
        case "fourth":
          submitData = { system: { swStorm: generatePortData(swStorm.value) } };
          break;
        case "fifth":
          submitData = { network: { swIsolate: generatePortData(swIsolate.value) } };
          break;
        case "sixth":
          submitData = { system: { swQos: generatePortData(swQos.value) } };
          break;
      }

      console.log("提交端口配置数据:", submitData);
      const success = await submitConfig(submitData);

      if (success) {
        portDialogVisible.value = false;
      }
    } catch (error) {
      console.error("端口配置提交失败:", error);
    }
  }
  return false;
};

onMounted(async () => {
  try {
    // 端口设置数据已通过 props 传入，无需额外加载
    console.log("PortSettings 组件已挂载");
  } catch (error) {
    console.error("Failed to initialize port settings:", error);
  } finally {
    portSettingsLoading.value = false;
  }
});
</script>

<style scoped lang="scss">
/* 端口容器 */
.sw-port-container {
  width: 100%;
  overflow: auto hidden; /* 水平滚动 */
}
.sw-port-list {
  display: flex;
  min-width: fit-content; /* 确保内容不被压缩 */
  padding: 0;
  margin: 0;
  list-style: none;
}

/* 单行显示（端口数 <= 5） */
.sw-port-list.single-row {
  flex-flow: row nowrap;
  gap: 10px;
  justify-content: flex-start;
}

/* 多行显示容器 */
.sw-port-multi-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  overflow-x: auto;
}

/* 每一行的端口列表 */
.sw-port-row {
  display: flex;
  flex-direction: row;
  gap: 8px;
  min-width: fit-content;
  padding: 0;
  margin: 0;
  list-style: none;
}
.sw-port-tag {
  box-sizing: border-box;
  display: flex;
  flex-shrink: 0; /* 防止压缩 */
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 60px; /* 增加宽度以容纳更长的名称 */
  min-height: 65px; /* 最小高度 */
  padding: 4px;
  margin: 1px;
  cursor: pointer;
  background-color: #f9f9f9;
  border: 1px solid transparent;
  border-radius: 5px;
  transition: all 0.3s ease;
}
.sw-port-list li {
  text-align: center; /* 文字居中 */
}

/* 端口图标样式 */
.port-icon-img {
  flex-shrink: 0; /* 防止图标被压缩 */
  width: 35px !important;
  height: 35px !important;
  margin-bottom: 2px;
}

/* 端口名称容器 */
.port-name-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 20px; /* 确保有足够的高度 */
  padding: 0 2px;
  overflow: hidden; /* 防止内容溢出 */
  .port-name-input {
    width: 100% !important;
    font-size: 10px;
    :deep(.el-input__inner) {
      padding: 2px 4px;
      font-size: 10px;
      text-align: center;
    }
  }
  .port-name-display {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    cursor: pointer;
    .port-name-text {
      display: -webkit-box;
      max-width: 100%;
      overflow: hidden;
      font-size: 10px !important;
      line-height: 1.2;
      text-align: center;
      -webkit-line-clamp: 2; /* 最多显示2行 */
      word-break: break-all; /* 允许长单词换行 */
      -webkit-box-orient: vertical;
    }
    .edit-icon {
      position: absolute;
      top: -2px;
      right: -2px;
      padding: 1px;
      font-size: 8px;
      visibility: hidden; // 默认隐藏
      background: rgb(255 255 255 / 80%);
      border-radius: 2px;
      opacity: 0;
      transition: opacity 0.2s;
    }
    &:hover .edit-icon {
      visibility: visible; // 悬浮时显示
      opacity: 1;
    }
  }
}

/* 端口标签悬停和选中效果 */
.sw-port-tag:hover {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  transform: translateY(-1px);
}
.sw-port-tag.selected-port {
  color: white;
  background-color: #1890ff;
  border-color: #1890ff;
  .port-name-text {
    color: white !important;
  }
  .edit-icon {
    color: white;
    background: rgb(255 255 255 / 20%);
  }
}
</style>
