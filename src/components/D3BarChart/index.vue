<template>
  <div ref="d3Container" class="d3-bar-chart"></div>
  <div ref="tooltipRef" class="d3-tooltip" :class="{ dark: isDark }" v-show="tooltip.visible" :style="tooltip.style">
    <div>{{ tooltip.deviceType }}</div>
    <div>{{ tooltip.status }}: {{ tooltip.value }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, computed } from "vue";
import * as d3 from "d3";

const props = defineProps<{
  yAxisData: string[];
  online: number[];
  offline: number[];
  isDark: boolean;
  legendOnline: string;
  legendOffline: string;
}>();

const d3Container = ref<HTMLDivElement | null>(null);
const tooltipRef = ref<HTMLDivElement | null>(null);
const isDark = computed(() => props.isDark);

// tooltip 状态
const tooltip = ref({
  visible: false,
  style: {},
  deviceType: "",
  status: "",
  value: 0
});

let resizeHandler: (() => void) | null = null;

// 优化后的 tooltip 位置
function showTooltip(event: MouseEvent, deviceType: string, status: string, value: number) {
  tooltip.value.visible = true;
  tooltip.value.deviceType = deviceType;
  tooltip.value.status = status;
  tooltip.value.value = value;
  // 计算相对于容器的坐标
  const container = d3Container.value;
  if (container) {
    const rect = container.getBoundingClientRect();
    const offsetX = 24;
    const offsetY = -10;
    let left = event.clientX - rect.left + offsetX;
    let top = event.clientY - rect.top + offsetY;
    const tooltipWidth = 140;
    if (rect.right - event.clientX < tooltipWidth) {
      left = event.clientX - rect.left - tooltipWidth - 10;
    }
    tooltip.value.style = {
      left: left + "px",
      top: top + "px"
    };
  }
}
function hideTooltip() {
  tooltip.value.visible = false;
}

function renderChart() {
  if (!d3Container.value) return;
  d3.select(d3Container.value).selectAll("*").remove();

  // 柱子更厚实
  const barHeight = 38;
  const groupGap = 22;
  const width = d3Container.value.clientWidth || 500;
  const height = props.yAxisData.length * (barHeight + groupGap) + 100;
  const margin = { top: 48, right: 40, bottom: 48, left: 120 };

  const svg = d3.select(d3Container.value).append("svg").attr("width", width).attr("height", height);

  // 颜色
  const onlineColor = props.isDark ? "#4fc3f7" : "#409EFF";
  const offlineColor = props.isDark ? "#ffb4b4" : "#e57373";
  const textColor = props.isDark ? "#fff" : "#333";
  const gridColor = props.isDark ? "#444" : "#eee";

  // x轴最大值
  const max = Math.max(...props.online, ...props.offline, 1);

  // x轴比例尺
  const x = d3
    .scaleLinear()
    .domain([0, max * 1.1])
    .range([margin.left, width - margin.right]);

  // y轴比例尺（分组）
  const y = d3
    .scaleBand()
    .domain(props.yAxisData)
    .range([margin.top, height - margin.bottom])
    .padding(0.22);

  // 分组内比例尺
  const y1 = d3.scaleBand().domain(["online", "offline"]).range([0, y.bandwidth()]).padding(0.18);

  // y轴标签
  svg
    .append("g")
    .selectAll("text")
    .data(props.yAxisData)
    .join("text")
    .attr("x", margin.left - 14)
    .attr("y", d => (y(d) ?? 0) + y.bandwidth() / 2 + 10)
    .attr("fill", textColor)
    .attr("font-weight", "bold")
    .attr("font-size", 18)
    .attr("text-anchor", "end")
    .text(d => d);

  // 分组柱状图
  const group = svg
    .append("g")
    .selectAll("g")
    .data(props.yAxisData)
    .join("g")
    .attr("transform", d => `translate(0,${y(d)})`);

  // 在线柱
  group
    .append("rect")
    .attr("x", x(0))
    .attr("y", y1("online"))
    .attr("width", (d, i) => x(props.online[i]) - x(0))
    .attr("height", y1.bandwidth())
    .attr("fill", onlineColor)
    .attr("rx", 8)
    .style("filter", "drop-shadow(0 4px 16px rgba(64,158,255,0.13))")
    .on("mousemove", function (event, d) {
      showTooltip(event, d, props.legendOnline, props.online[props.yAxisData.indexOf(d)]);
    })
    .on("mouseleave", hideTooltip);

  // 离线柱
  group
    .append("rect")
    .attr("x", x(0))
    .attr("y", y1("offline"))
    .attr("width", (d, i) => x(props.offline[i]) - x(0))
    .attr("height", y1.bandwidth())
    .attr("fill", offlineColor)
    .attr("rx", 8)
    .style("filter", "drop-shadow(0 4px 16px rgba(229,115,115,0.13))")
    .on("mousemove", function (event, d) {
      showTooltip(event, d, props.legendOffline, props.offline[props.yAxisData.indexOf(d)]);
    })
    .on("mouseleave", hideTooltip);

  // 在线数值
  group
    .append("text")
    .attr("x", (d, i) => x(props.online[i]) + 10)
    .attr("y", y1("online") + y1.bandwidth() / 2 + 10)
    .attr("fill", textColor)
    .attr("font-size", 17)
    .attr("font-weight", "bold")
    .attr("text-anchor", "start")
    .text((d, i) => (props.online[i] > 0 ? props.online[i] : ""));

  // 离线数值
  group
    .append("text")
    .attr("x", (d, i) => x(props.offline[i]) + 10)
    .attr("y", y1("offline") + y1.bandwidth() / 2 + 10)
    .attr("fill", textColor)
    .attr("font-size", 17)
    .attr("font-weight", "bold")
    .attr("text-anchor", "start")
    .text((d, i) => (props.offline[i] > 0 ? props.offline[i] : ""));

  // x轴
  svg
    .append("g")
    .attr("transform", `translate(0,${height - margin.bottom})`)
    .call(d3.axisBottom(x).ticks(6))
    .call(g => g.selectAll(".domain, .tick line").attr("stroke", gridColor))
    .call(g => g.selectAll("text").attr("fill", textColor).attr("font-size", 15));

  // 图例美化
  const legend = svg.append("g").attr("transform", `translate(${width - 180},${margin.top - 30})`);
  legend
    .append("rect")
    .attr("x", 0)
    .attr("y", 0)
    .attr("width", 18)
    .attr("height", 18)
    .attr("fill", onlineColor)
    .attr("rx", 7)
    .attr("stroke", "#fff")
    .attr("stroke-width", 1.5);
  legend
    .append("text")
    .attr("x", 26)
    .attr("y", 14)
    .attr("fill", textColor)
    .attr("font-size", 16)
    .attr("font-weight", "bold")
    .text(props.legendOnline);
  legend
    .append("rect")
    .attr("x", 80)
    .attr("y", 0)
    .attr("width", 18)
    .attr("height", 18)
    .attr("fill", offlineColor)
    .attr("rx", 7)
    .attr("stroke", "#fff")
    .attr("stroke-width", 1.5);
  legend
    .append("text")
    .attr("x", 106)
    .attr("y", 14)
    .attr("fill", textColor)
    .attr("font-size", 16)
    .attr("font-weight", "bold")
    .text(props.legendOffline);
}

watch(() => [props.yAxisData, props.online, props.offline, props.isDark, props.legendOnline, props.legendOffline], renderChart, {
  deep: true
});

onMounted(() => {
  renderChart();
  resizeHandler = () => renderChart();
  window.addEventListener("resize", resizeHandler);
});
onBeforeUnmount(() => {
  if (resizeHandler) window.removeEventListener("resize", resizeHandler);
});
</script>

<style scoped>
.d3-bar-chart {
  position: relative;
  width: 100%;
  height: 350px;
  min-height: 400px;
  background: transparent;
}
.d3-tooltip {
  position: absolute;
  z-index: 9999;
  min-width: 100px;
  padding: 10px 18px;
  font-size: 15px;
  line-height: 1.7;
  color: #333333;
  pointer-events: none;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 18px rgba(0 0 0 / 13%);
  opacity: 0.98;
  transition: opacity 0.1s;
}
.d3-tooltip.dark {
  color: #ffffff;
  background: #222222;
  box-shadow: 0 4px 18px rgba(0 0 0 / 32%);
}
</style>
