<template>
  <el-card>
    <el-form-item :label="t('device.lanIp')" prop="modelValue.ipaddr" label-position="left" label-width="160px">
      <el-input
        :model-value="props.modelValue.ipaddr"
        @input="updateIpaddr"
        :placeholder="t('device.lanIpTip')"
        clearable
      ></el-input>
    </el-form-item>
    <el-form-item :label="t('device.lanNetmask')" prop="modelValue.netmask" label-position="left" label-width="160px">
      <el-input
        :model-value="props.modelValue.netmask"
        @input="updateNetmask"
        :placeholder="t('device.lanNetmaskTip')"
        clearable
      ></el-input>
    </el-form-item>
  </el-card>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(["update:modelValue"]);

const updateIpaddr = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, ipaddr: value });
};

const updateNetmask = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, netmask: value });
};
</script>
