.filter {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 220px;
  min-width: 220px;
  height: 100%;
  padding: 18px;
  margin-right: 10px;
  .title {
    margin: 0 0 15px;
    font-size: 18px;
    font-weight: bold;
    color: var(--el-color-info-dark-2);
    letter-spacing: 0.5px;
  }
  .search {
    display: flex;
    align-items: center;
    margin: 0 0 15px;
    .el-icon {
      cursor: pointer;
      transform: rotate(90deg) translateY(-8px);
    }
  }
  .el-scrollbar {
    :deep(.el-tree) {
      height: 80%;
      overflow: auto;
      .el-tree-node__content {
        height: 33px;
      }
      .el-tree-node__label {
        width: 150px;
        margin-right: 33px;
        overflow: hidden;
        color: #222222; // 修正：未选中标签文字为黑色，顺序调整
        text-align: center;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    :deep(.el-tree--highlight-current) {
      .el-tree-node.is-current > .el-tree-node__content {
        background-color: color-mix(in srgb, var(--el-color-primary) 80%, transparent);
        backdrop-filter: blur(8px);
        border-radius: 5px;
        .el-tree-node__label,
        .el-tree-node__expand-icon {
          color: white;
        }
        .is-leaf {
          color: transparent;
        }
      }
    }
  }
}

// 暗黑模式下 TreeFilter 菜单优化
.dark .card.filter {
  color: #f5f6fa !important; // 亮灰字体
  background: #23272e !important; // 深灰背景
  .el-tree-node__content {
    color: #f5f6fa !important; // 普通项字体
  }
  .el-tree-node__content:hover,
  .el-tree-node.is-current > .el-tree-node__content {
    color: #ffffff !important; // 悬停/选中字体
    background: #2d323b !important; // 悬停/选中背景
  }
  .el-tree-node__label {
    color: inherit !important; // 保证label跟随父级
  }
  .title {
    color: #ffffff !important; // 标题更亮
  }
}
