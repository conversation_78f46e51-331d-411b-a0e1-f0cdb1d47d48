<template>
  <el-card class="box-card">
    <el-form-item :label="t('device.dnsEnabled')" prop="ip" label-position="left" label-width="160px">
      <el-select
        :model-value="props.modelValue.dnsenabled"
        @update:model-value="updateField('dnsenabled', $event)"
        :placeholder="t('device.dhcpTip')"
      >
        <el-option :label="t('device.dnsModify')" :value="1"></el-option>
        <el-option :label="t('device.dnsAuto')" :value="0"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      :label="t('device.dns1')"
      prop="modelValue.dns1"
      label-position="left"
      label-width="160px"
      v-if="props.modelValue && props.modelValue.dnsenabled === 1"
    >
      <el-input
        :model-value="props.modelValue.dns1"
        @input="updateField('dns1', $event)"
        :placeholder="$t('device.dns1Placeholder')"
        clearable
      ></el-input>
    </el-form-item>
    <el-form-item
      :label="t('device.dns2')"
      prop="modelValue.dns2"
      label-position="left"
      label-width="160px"
      v-if="props.modelValue && props.modelValue.dnsenabled === 1"
    >
      <el-input
        :model-value="props.modelValue.dns2"
        @input="updateField('dns2', $event)"
        :placeholder="$t('device.dns2Placeholder')"
        clearable
      ></el-input>
    </el-form-item>
  </el-card>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";

interface DnsConfig {
  dnsenabled: number;
  dns1: string;
  dns2: string;
}

interface Props {
  modelValue: DnsConfig;
}

interface Emits {
  (e: "update:modelValue", value: DnsConfig): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const { t } = useI18n();

// 更新字段的通用方法
const updateField = (field: keyof DnsConfig, value: any) => {
  const updatedConfig = { ...props.modelValue, [field]: value };
  emit("update:modelValue", updatedConfig);
};
</script>

<style scoped>
.box-card {
  /* 继承父组件样式 */
}
</style>
