.device-img {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
  object-fit: contain; /* 确保图片比例适配 */
}

/* 端口容器 */
.sw-port-container {
  width: 100%;
  overflow: auto hidden; /* 水平滚动 */
}
.sw-port-list {
  display: flex;
  min-width: fit-content; /* 确保内容不被压缩 */
  padding: 0;
  margin: 0;
  list-style: none;
}

/* 单行显示（端口数 <= 5） */
.sw-port-list.single-row {
  flex-flow: row nowrap;
  gap: 10px;
  justify-content: flex-start;
}

/* 多行显示容器 */
.sw-port-multi-row {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  overflow-x: auto;
}

/* 每一行的端口列表 */
.sw-port-row {
  display: flex;
  flex-direction: row;
  gap: 8px;
  min-width: fit-content;
  padding: 0;
  margin: 0;
  list-style: none;
}
.sw-port-tag {
  box-sizing: border-box;
  display: flex;
  flex-shrink: 0; /* 防止压缩 */
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 60px; /* 增加宽度以容纳更长的名称 */
  min-height: 65px; /* 最小高度 */
  padding: 4px;
  margin: 1px;
  background-color: #f9f9f9;
  border: 1px solid transparent;
  border-radius: 5px;
  transition: all 0.3s ease;
}
.sw-port-list li {
  text-align: center; /* 文字居中 */
}
.sw-port-example {
  display: flex;
  flex-shrink: 0; /* 防止内容缩小导致换行 */
  align-items: center;
  justify-content: flex-start;
  margin-right: 15px; /* 给每个项增加右边距，确保之间有间隔 */
  white-space: nowrap; /* 确保文字不换行 */
}
.port-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px; /* 图标与文字之间的间距 */
}
.port-text {
  line-height: 20px; /* 保证文字与图标垂直居中 */
  white-space: nowrap; /* 确保文字不换行 */
}

/* 特殊样式 */
.selected-port {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff; /* 设置边框宽度为 1px */
  border-radius: 5px; /* 可选，增加圆角效果 */
}
.edit-cell {
  min-height: 32px;
  padding: 5px;
  line-height: 32px;
  cursor: pointer;
  &:hover {
    background-color: #f5f7fa;
  }
}

@media screen and (width <= 768px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 12px 16px;
      font-size: 16px;
      border-bottom: 1px solid #ebeef5;
    }
    .el-drawer__body {
      padding: 12px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 16px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 6px !important;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.3;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          font-size: 14px;
          .el-input {
            .el-input__inner {
              padding: 10px 12px;
              font-size: 14px;
            }
          }
          .el-select {
            width: 100%;
            .el-input__inner {
              padding: 10px 12px;
              font-size: 14px;
            }
          }
          .el-text {
            font-size: 14px;
            line-height: 1.4;
          }
        }
      }
    }
    .el-tabs {
      .el-tabs__item {
        min-width: auto;
        padding: 0 12px;
        font-size: 14px;
      }
      .el-tabs__content {
        padding: 12px 0;
      }
    }
    .el-card {
      margin: 10px 0 !important;
      .el-card__body {
        padding: 12px;
      }
      .el-card__header {
        padding: 12px;
        font-size: 14px;
        font-weight: 500;
      }
    }
    .el-collapse {
      .el-collapse-item__header {
        padding: 0 12px;
        font-size: 14px;
        font-weight: 500;
      }
      .el-collapse-item__content {
        padding: 12px;
      }
    }

    // 端口图标优化
    .sw-port-tag {
      width: 45px;
      height: 60px;
      padding: 4px;
      .el-image {
        width: 30px !important;
        height: 30px !important;
      }
      .el-text {
        font-size: 11px;
      }
    }
    .sw-port-list {
      gap: 6px;
    }

    // 设备图标
    .device-img {
      width: 60px;
      height: 60px;
    }

    // 按钮优化
    .el-drawer__footer {
      padding: 12px 16px;
      text-align: center;
      border-top: 1px solid #ebeef5;
      .el-button {
        min-width: 80px;
        padding: 8px 20px;
        font-size: 14px;
        &:first-child {
          margin-right: 12px;
        }
      }
    }

    // 表格优化
    .el-table {
      font-size: 13px;
      .el-table__header th {
        padding: 8px 0;
        font-size: 13px;
      }
      .el-table__body td {
        padding: 8px 0;
        font-size: 13px;
      }
    }
  }
}

@media screen and (width <= 480px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 10px 12px;
      font-size: 14px;
    }
    .el-drawer__body {
      padding: 8px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 12px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 4px !important;
          font-size: 12px;
          font-weight: 500;
          line-height: 1.2;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          font-size: 12px;
          .el-input {
            .el-input__inner {
              padding: 8px 10px;
              font-size: 12px;
            }
          }
          .el-select {
            width: 100%;
            .el-input__inner {
              padding: 8px 10px;
              font-size: 12px;
            }
          }
          .el-text {
            font-size: 12px;
            line-height: 1.3;
          }
        }
      }
    }
    .el-tabs {
      .el-tabs__item {
        min-width: auto;
        padding: 0 6px;
        font-size: 11px;
      }
      .el-tabs__content {
        padding: 8px 0;
      }
    }
    .el-card {
      padding: 6px !important;
      margin: 6px 0 !important;
      .el-card__body {
        padding: 6px;
      }
      .el-card__header {
        padding: 8px;
        font-size: 12px;
      }
    }
    .el-collapse {
      .el-collapse-item__header {
        padding: 0 8px;
        font-size: 12px;
      }
      .el-collapse-item__content {
        padding: 8px;
      }
    }

    // 端口图标优化
    .sw-port-tag {
      width: 38px;
      height: 48px;
      padding: 2px;
      .el-image {
        width: 24px !important;
        height: 24px !important;
      }
      .el-text {
        font-size: 9px;
        line-height: 1.1;
      }
    }
    .sw-port-list {
      gap: 3px;
    }

    // 设备图标
    .device-img {
      width: 50px;
      height: 50px;
    }

    // 按钮优化
    .el-drawer__footer {
      padding: 8px 12px;
      .el-button {
        min-width: 60px;
        padding: 6px 12px;
        font-size: 12px;
      }
    }

    // 表格优化
    .el-table {
      font-size: 11px;
      .el-table__header th {
        padding: 6px 0;
        font-size: 11px;
      }
      .el-table__body td {
        padding: 6px 0;
        font-size: 11px;
      }
    }
  }
}

@media screen and (width <= 320px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 8px 10px;
      font-size: 13px;
    }
    .el-drawer__body {
      padding: 6px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 10px;
        .el-form-item__label {
          padding: 0 0 3px !important;
          font-size: 11px;
        }
        .el-form-item__content {
          font-size: 11px;
          .el-input {
            .el-input__inner {
              padding: 6px 8px;
              font-size: 11px;
            }
          }
          .el-select {
            .el-input__inner {
              padding: 6px 8px;
              font-size: 11px;
            }
          }
          .el-text {
            font-size: 11px;
          }
        }
      }
    }
    .el-tabs {
      .el-tabs__item {
        padding: 0 4px;
        font-size: 10px;
      }
      .el-tabs__content {
        padding: 6px 0;
      }
    }
    .el-card {
      padding: 4px !important;
      margin: 4px 0 !important;
      .el-card__body {
        padding: 4px;
      }
      .el-card__header {
        padding: 6px;
        font-size: 11px;
      }
    }

    // 设备图标
    .device-img {
      width: 40px;
      height: 40px;
    }

    // 按钮优化
    .el-drawer__footer {
      padding: 6px 8px;
      .el-button {
        min-width: 50px;
        padding: 4px 8px;
        font-size: 11px;
      }
    }
  }
}

/* 端口图标样式 */
.port-icon-img {
  flex-shrink: 0; /* 防止图标被压缩 */
  width: 35px !important;
  height: 35px !important;
  margin-bottom: 2px;
}

/* 端口名称容器 */
.port-name-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 20px; /* 确保有足够的高度 */
  padding: 0 2px;
  overflow: hidden; /* 防止内容溢出 */
  .port-name-input {
    width: 100% !important;
    font-size: 10px;
    :deep(.el-input__inner) {
      padding: 2px 4px;
      font-size: 10px;
      text-align: center;
    }
  }
  .port-name-display {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    cursor: pointer;
    .port-name-text {
      display: -webkit-box;
      max-width: 100%;
      overflow: hidden;
      font-size: 10px !important;
      line-height: 1.2;
      text-align: center;
      -webkit-line-clamp: 2; /* 最多显示2行 */
      word-break: break-all; /* 允许长单词换行 */
      -webkit-box-orient: vertical;
    }
    .edit-icon {
      position: absolute;
      top: -2px;
      right: -2px;
      padding: 1px;
      font-size: 8px;
      visibility: hidden; // 默认隐藏
      background: rgb(255 255 255 / 80%);
      border-radius: 2px;
      opacity: 0;
      transition: opacity 0.2s;
    }
    &:hover .edit-icon {
      visibility: visible; // 悬浮时显示
      opacity: 1;
    }
  }
}

/* 端口标签悬停和选中效果 */
.sw-port-tag:hover {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  transform: translateY(-1px);
}
.sw-port-tag.selected-port {
  color: white;
  background-color: #1890ff;
  border-color: #1890ff;
  .port-name-text {
    color: white !important;
  }
  .edit-icon {
    color: white;
    background: rgb(255 255 255 / 20%);
  }
}
