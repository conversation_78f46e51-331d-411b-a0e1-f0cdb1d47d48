import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { FormInstance } from "element-plus";
import { deviceConfig, editName, deviceNameChanged } from "@/api/interface/deviceConfigDrawer";

/**
 * 设备基础信息管理
 */
export function useDeviceBasic() {
  const { t } = useI18n();
  const tempDeviceName = ref("");

  // 显示设备名称
  const displayDeviceName = computed(() => {
    // 处理 deviceName 可能是对象的情况
    if (deviceConfig.deviceName) {
      if (typeof deviceConfig.deviceName === "string") {
        return deviceConfig.deviceName;
      } else if (typeof deviceConfig.deviceName === "object") {
        // 如果是对象，尝试提取字符串值
        console.warn("设备名称是对象类型:", deviceConfig.deviceName);
        // 尝试从对象中提取值
        const deviceNameObj = deviceConfig.deviceName as any;
        if (deviceNameObj.value !== undefined) {
          return deviceNameObj.value;
        } else if (deviceNameObj.name !== undefined) {
          return deviceNameObj.name;
        } else if (deviceNameObj.text !== undefined) {
          return deviceNameObj.text;
        } else {
          // 如果无法提取，返回默认值
          return t("device.unnamed");
        }
      }
    }
    return t("device.unnamed");
  });

  // 编辑设备名称
  const editDeviceName = () => {
    // 处理设备名称可能是对象的情况
    let deviceNameValue = deviceConfig.deviceName;
    if (deviceConfig.deviceName && typeof deviceConfig.deviceName === "object") {
      const deviceNameObj = deviceConfig.deviceName as any;
      deviceNameValue = deviceNameObj.value || deviceNameObj.name || deviceNameObj.text || "";
    }
    tempDeviceName.value = deviceNameValue;
    editName.value = true;
  };

  // 取消编辑
  const cancelEdit = (ruleFormRef?: FormInstance) => {
    // 确保恢复的是字符串值
    deviceConfig.deviceName = tempDeviceName.value;
    editName.value = false;
    deviceNameChanged.value = false;
    // 清除校验错误信息
    if (ruleFormRef) {
      ruleFormRef.clearValidate("deviceName");
    }
  };

  // 响应式drawer宽度
  const drawerSize = computed(() => {
    if (window.innerWidth <= 320) return "100vw";
    if (window.innerWidth <= 480) return "98vw";
    if (window.innerWidth <= 768) return "90vw";
    if (window.innerWidth <= 1024) return "80vw";
    return "680px";
  });

  return {
    displayDeviceName,
    editDeviceName,
    cancelEdit,
    drawerSize,
    tempDeviceName
  };
}
