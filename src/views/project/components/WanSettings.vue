<template>
  <el-card>
    <el-form-item :label="t('device.proto')" prop="modelValue.proto" label-position="left" label-width="160px">
      <el-select
        :model-value="props.modelValue.proto"
        @update:model-value="updateProto"
        :placeholder="t('common.pleaseSelect')"
        clearable
      >
        <el-option :label="t('device.dhcp')" value="dhcp"></el-option>
        <el-option :label="t('device.static')" value="static"></el-option>
        <el-option :label="t('device.pppoe')" value="pppoe"></el-option>
      </el-select>
    </el-form-item>
    <!-- PPPoE 配置 -->
    <template v-if="props.modelValue.proto === 'pppoe'">
      <el-form-item :label="t('device.wanUsername')" prop="modelValue.username" label-position="left" label-width="160px">
        <el-input
          :model-value="props.modelValue.username"
          @input="updateUsername"
          :placeholder="t('device.wanUsernamePlaceholder')"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item :label="t('device.wanPassword')" prop="modelValue.password" label-position="left" label-width="160px">
        <el-input
          type="password"
          :model-value="props.modelValue.password"
          @input="updatePassword"
          :placeholder="t('device.wanPasswordPlaceholder')"
          show-password
          clearable
        ></el-input>
      </el-form-item>
    </template>

    <!-- Static 配置 -->
    <template v-if="props.modelValue.proto === 'static'">
      <el-form-item :label="t('device.wanIp')" prop="modelValue.ipaddr" label-position="left" label-width="160px">
        <el-input
          :model-value="props.modelValue.ipaddr"
          @input="updateIpaddr"
          :placeholder="t('device.wanIpPlaceholder')"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item :label="t('device.wanNetmask')" prop="modelValue.netmask" label-position="left" label-width="160px">
        <el-input
          :model-value="props.modelValue.netmask"
          @input="updateNetmask"
          :placeholder="t('device.wanNetmaskPlaceholder')"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item :label="t('device.wanGateway')" prop="modelValue.gawa" label-position="left" label-width="160px">
        <el-input
          :model-value="props.modelValue.gawa"
          @input="updateGawa"
          :placeholder="t('device.wanGatewayPlaceholder')"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item :label="t('device.dns1')" prop="modelValue.dns1" label-position="left" label-width="160px">
        <el-input
          :model-value="props.modelValue.dns1"
          @input="updateDns1"
          :placeholder="t('device.dns1Placeholder')"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item :label="t('device.dns2')" prop="modelValue.dns2" label-position="left" label-width="160px">
        <el-input
          :model-value="props.modelValue.dns2"
          @input="updateDns2"
          :placeholder="t('device.dns2Placeholder')"
          clearable
        ></el-input>
      </el-form-item>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});
const emit = defineEmits(["update:modelValue"]);

const updateProto = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, proto: value });
};

const updateUsername = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, username: value });
};

const updatePassword = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, password: value });
};

const updateIpaddr = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, ipaddr: value });
};

const updateNetmask = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, netmask: value });
};

const updateGawa = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, gawa: value });
};

const updateDns1 = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, dns1: value });
};

const updateDns2 = (value: string) => {
  emit("update:modelValue", { ...props.modelValue, dns2: value });
};
</script>
