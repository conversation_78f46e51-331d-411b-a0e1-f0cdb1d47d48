{"editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.stylelint": "explicit"}, "stylelint.enable": true, "stylelint.validate": ["css", "less", "postcss", "scss", "vue", "sass", "html"], "files.eol": "\n", "typescript.tsdk": "node_modules/typescript/lib", "[vue]": {"editor.defaultFormatter": "Vue.volar"}, "[typescript]": {"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[less]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "cSpell.words": ["AMAP", "apng", "axios", "Biao", "brotli", "cascader", "commitlint", "contentleft", "contentright", "CSDN", "daterange", "datetimerange", "echarts", "fangda", "geeker", "<PERSON><PERSON><PERSON>", "hexs", "huiche", "iconfont", "jue<PERSON>", "liquidfill", "longpress", "monthrange", "nprogress", "officedocument", "openxmlformats", "Pageable", "persistedstate", "pinia", "pjpeg", "Prefixs", "screenfull", "sortablejs", "sousuo", "spreadsheetml", "styl", "stylelint", "<PERSON><PERSON><PERSON><PERSON>", "stylelintrc", "su<PERSON>iao", "truetype", "tuichu", "unplugin", "unref", "VITE", "vuedraggable", "vueuse", "Vuex", "wangeditor", "xiala", "xiaoxi", "<PERSON><PERSON><PERSON>", "yiwen", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "i18n-ally.localesPaths": ["src/languages"], "vetur.format.defaultFormatter.js": "prettier-es<PERSON>"}