import { ref } from "vue";
import { deviceConfig } from "@/api/interface/deviceConfigDrawer";

/**
 * WiFi设置管理
 */
export function useWifiSettings() {
  // Guest速率选项
  const guestRateOptions = ref([
    { label: "1 Mbps", value: 1 },
    { label: "2 Mbps", value: 2 },
    { label: "5.5 Mbps", value: 5.5 },
    { label: "6 Mbps", value: 6 },
    { label: "9 Mbps", value: 9 },
    { label: "11 Mbps", value: 11 },
    { label: "12 Mbps", value: 12 },
    { label: "18 Mbps", value: 18 },
    { label: "24 Mbps", value: 24 },
    { label: "36 Mbps", value: 36 },
    { label: "48 Mbps", value: 48 },
    { label: "54 Mbps", value: 54 }
  ]);

  // 处理Guest速率变化
  const handleGuestRateChange = (value: number) => {
    console.log("Guest速率变化:", value);
    if (deviceConfig.wireless?.guest) {
      deviceConfig.wireless.guest.rate = value;
    }
  };

  // 初始化wireless配置
  const initWirelessConfig = () => {
    if (!deviceConfig.wireless) {
      deviceConfig.wireless = {
        wifiTime: {
          enabled: 0,
          beginTime: "00:00",
          endTime: "23:59"
        },
        radio0: {
          disabled: 0,
          ssid: "",
          key: "",
          hidden: 0,
          channel: "auto",
          bandwidth: "auto",
          power: 100,
          mode: "11bgn",
          security: "psk2"
        },
        radio1: {
          disabled: 0,
          ssid: "",
          key: "",
          hidden: 0,
          channel: "auto",
          bandwidth: "auto",
          power: 100,
          mode: "11an",
          security: "psk2"
        },
        guest: {
          disabled: 1,
          ssid: "",
          key: "",
          hidden: 0,
          rate: 54,
          security: "none"
        }
      };
    }
  };

  return {
    guestRateOptions,
    handleGuestRateChange,
    initWirelessConfig
  };
}
