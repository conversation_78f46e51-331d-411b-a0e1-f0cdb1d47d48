.el-container {
  width: 100%;
  height: 100%;
  .aside-split {
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    width: 70px;
    height: 100%;
    background-color: var(--el-menu-bg-color);
    border-right: 1px solid var(--el-aside-border-color);
    .logo {
      box-sizing: border-box;
      height: 55px;
      .logo-img {
        width: 32px;
        height: 32px;
        object-fit: contain;
      }
    }
    .el-scrollbar {
      height: calc(100% - 55px);
      .split-list {
        flex: 1;
        .split-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 70px;
          cursor: pointer;
          transition: all 0.3s ease;
          .el-icon {
            font-size: 20px;
          }
          .title {
            margin-top: 6px;
            font-size: 12px;
          }
          .el-icon,
          .title {
            color: var(--el-menu-text-color);
          }
        }
        .split-active {
          background-color: var(--el-color-primary) !important;
          .el-icon,
          .title {
            color: #ffffff !important;
          }
        }
      }
    }
  }
  .not-aside {
    width: 0 !important;
    border-right: none !important;
  }
  .el-aside {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    background-color: var(--el-menu-bg-color);
    border-right: 1px solid var(--el-aside-border-color);
    transition: width 0.3s ease;
    .el-scrollbar {
      height: calc(100% - 55px);
      .el-menu {
        width: 100%;
        overflow-x: hidden;
        border-right: none;
      }
    }
    .logo {
      box-sizing: border-box;
      height: 55px;
      .logo-text {
        font-size: 24px;
        font-weight: bold;
        color: var(--el-aside-logo-text-color);
        white-space: nowrap;
      }
    }
  }
  .el-header {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    padding: 0 15px;
    background-color: var(--el-header-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
  }
}
