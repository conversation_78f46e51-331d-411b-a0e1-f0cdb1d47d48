@media screen and (width <= 480px) {
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 12px 16px;
      .el-dialog__title {
        font-size: 14px;
        line-height: 1.3;
      }
    }
    .el-dialog__body {
      padding: 12px 16px;
    }
    .el-dialog__footer {
      padding: 8px 16px;
      text-align: center;
      .el-button {
        width: 80px;
        padding: 8px 16px;
        font-size: 13px;
        &:first-child {
          margin-right: 12px;
        }
      }
    }
    .el-form {
      .el-form-item {
        margin-bottom: 16px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 6px !important;
          margin-bottom: 0;
          font-size: 13px;
          line-height: 1.3;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          .el-input {
            font-size: 14px;
            .el-input__inner {
              padding: 8px 12px;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

@media screen and (width <= 320px) {
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 10px 12px;
      .el-dialog__title {
        font-size: 13px;
      }
    }
    .el-dialog__body {
      padding: 8px 12px;
    }
    .el-dialog__footer {
      padding: 6px 12px;
      .el-button {
        width: 70px;
        padding: 6px 12px;
        font-size: 12px;
      }
    }
  }
}
