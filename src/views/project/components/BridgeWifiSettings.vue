<template>
  <el-card shadow="always" :style="{ marginTop: '10px', marginBottom: '10px' }" class="box-card">
    <el-main>
      <el-form-item :label="$t('device.name')" label-position="left" label-width="160px" prop="modelValue.ssid">
        <el-input :model-value="props.modelValue.ssid" @input="updateField('ssid', $event)" clearable />
      </el-form-item>
      <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
        <el-switch
          :model-value="props.modelValue.hidden"
          @update:model-value="updateField('hidden', $event)"
          :active-value="1"
          :inactive-value="0"
        />
      </el-form-item>
      <el-form-item :label="$t('device.key')" label-position="left" label-width="160px" prop="modelValue.key">
        <el-input
          type="password"
          :model-value="props.modelValue.key"
          @input="updateField('key', $event)"
          show-password
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('device.channel')" label-position="left" label-width="160px">
        <el-select :model-value="props.modelValue.channel" @update:model-value="updateField('channel', $event)">
          <el-option :label="t('common.auto')" :value="0" />
          <el-option v-for="channel in props.modelValue.chanList" :key="channel" :label="channel" :value="channel" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('device.txpower')" label-position="left" label-width="160px">
        <el-select :model-value="props.modelValue.txpower" @update:model-value="updateField('txpower', $event)" clearable>
          <el-option
            v-for="percent in [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
            :key="percent"
            :label="`${percent}%`"
            :value="percent"
          />
        </el-select>
      </el-form-item>
    </el-main>
  </el-card>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";

interface BridgeWifiConfig {
  ssid: string;
  hidden: number;
  key: string;
  channel: number;
  txpower: number;
  chanList: number[];
}

interface Props {
  modelValue: BridgeWifiConfig;
}

interface Emits {
  (e: "update:modelValue", value: BridgeWifiConfig): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const { t } = useI18n();

// 更新字段的通用方法
const updateField = (field: keyof BridgeWifiConfig, value: any) => {
  const updatedConfig = { ...props.modelValue, [field]: value };
  emit("update:modelValue", updatedConfig);
};
</script>

<style scoped>
.box-card {
  margin-top: 10px;
  margin-bottom: 10px;
}
</style>
