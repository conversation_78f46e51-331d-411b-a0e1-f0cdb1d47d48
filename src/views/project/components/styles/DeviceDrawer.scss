@media screen and (width <= 768px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 14px 18px;
      font-size: 15px;
      border-bottom: 1px solid #ebeef5;
    }
    .el-drawer__body {
      padding: 16px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 18px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 6px !important;
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          font-size: 14px;
          .el-input {
            .el-input__inner {
              padding: 10px 12px;
              font-size: 14px;
              line-height: 1.4;
            }
          }
          .el-select {
            width: 100%;
            .el-input__inner {
              padding: 10px 12px;
              font-size: 14px;
            }
          }
          .el-text {
            font-size: 14px;
            line-height: 1.5;
            color: #606266;
          }
        }
      }
    }
    .el-drawer__footer {
      padding: 12px 16px;
      text-align: center;
      border-top: 1px solid #ebeef5;
      .el-button {
        min-width: 80px;
        padding: 8px 20px;
        font-size: 14px;
        &:first-child {
          margin-right: 12px;
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 12px 16px;
      font-size: 14px;
    }
    .el-drawer__body {
      padding: 12px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 14px;
        .el-form-item__label {
          width: 100% !important;
          padding: 0 0 5px !important;
          font-size: 13px;
          font-weight: 500;
          text-align: left !important;
        }
        .el-form-item__content {
          margin-left: 0 !important;
          font-size: 13px;
          .el-input {
            .el-input__inner {
              padding: 8px 10px;
              font-size: 13px;
            }
          }
          .el-select {
            width: 100%;
            .el-input__inner {
              padding: 8px 10px;
              font-size: 13px;
            }
          }
          .el-text {
            font-size: 13px;
            line-height: 1.4;
          }
        }
      }
    }
    .el-drawer__footer {
      padding: 10px 12px;
      .el-button {
        min-width: 70px;
        padding: 7px 16px;
        font-size: 13px;
      }
    }
  }
}

@media screen and (width <= 320px) {
  :deep(.el-drawer) {
    .el-drawer__header {
      padding: 10px 12px;
      font-size: 13px;
    }
    .el-drawer__body {
      padding: 8px;
    }
    .el-form {
      .el-form-item {
        margin-bottom: 12px;
        .el-form-item__label {
          padding: 0 0 4px !important;
          font-size: 12px;
        }
        .el-form-item__content {
          font-size: 12px;
          .el-input {
            .el-input__inner {
              padding: 6px 8px;
              font-size: 12px;
            }
          }
          .el-select {
            .el-input__inner {
              padding: 6px 8px;
              font-size: 12px;
            }
          }
          .el-text {
            font-size: 12px;
          }
        }
      }
    }
    .el-drawer__footer {
      padding: 8px 10px;
      .el-button {
        min-width: 60px;
        padding: 6px 12px;
        font-size: 12px;
      }
    }
  }
}
