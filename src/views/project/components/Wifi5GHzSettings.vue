<template>
  <el-card
    class="box-card"
    shadow="always"
    :style="{ marginTop: '10px', marginBottom: '10px' }"
    v-if="supports.includes('radio1') && modelValue"
  >
    <el-header>
      <el-form-item :label="$t('device.wifi5GHz')" label-position="left" label-width="160px">
        <el-switch
          :model-value="modelValue.disabled"
          @update:model-value="updateField('disabled', $event)"
          active-color="#13ce66"
          :active-value="0"
          :inactive-value="1"
          inactive-color="#ff4949"
          :active-text="$t('device.open')"
          :inactive-text="$t('device.close')"
        />
      </el-form-item>
    </el-header>
    <el-main v-if="modelValue.disabled === 0">
      <el-form-item :label="$t('device.name')" label-position="left" label-width="160px" prop="ssid">
        <el-input :model-value="modelValue.ssid" @update:model-value="updateField('ssid', $event)" clearable />
      </el-form-item>
      <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
        <el-switch
          :model-value="modelValue.hidden"
          @update:model-value="updateField('hidden', $event)"
          :active-value="1"
          :inactive-value="0"
        />
      </el-form-item>
      <el-form-item :label="$t('device.encryptionMethod')" label-position="left" label-width="160px">
        <el-select :model-value="encryptionMethod" @update:model-value="updateEncryptionMethod">
          <el-option :label="$t('device.encryption')" :value="true" />
          <el-option :label="$t('device.encryptionNone')" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('device.key')"
        label-position="left"
        label-width="160px"
        v-if="encryptionMethod === true"
        prop="key"
      >
        <el-input
          type="password"
          :model-value="modelValue.key"
          @update:model-value="updateField('key', $event)"
          show-password
          clearable
          @clear="$emit('passwordClear', 'radio1')"
        />
      </el-form-item>
      <el-form-item :label="$t('device.channel')" label-position="left" label-width="160px">
        <el-select :model-value="modelValue.channel" @update:model-value="updateField('channel', $event)">
          <el-option :label="$t('common.auto')" :value="0" />
          <el-option v-for="channel in modelValue.chanList" :key="channel" :label="channel" :value="channel" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('device.txpower')" label-position="left" label-width="160px">
        <el-select :model-value="modelValue.txpower" @update:model-value="updateField('txpower', $event)">
          <el-option
            v-for="percent in [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
            :key="percent"
            :label="`${percent}%`"
            :value="percent"
          />
        </el-select>
      </el-form-item>
    </el-main>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from "vue";
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  supports: {
    type: Array,
    default: () => []
  }
});
const emit = defineEmits(["update:modelValue", "passwordClear"]);

const updateField = (field: string, value: any) => {
  const newValue = { ...props.modelValue };
  newValue[field] = value;
  emit("update:modelValue", newValue);
};

const encryptionMethod = computed(() => props.modelValue.key !== "");

const updateEncryptionMethod = (value: boolean) => {
  const newValue = { ...props.modelValue };
  newValue.key = value ? newValue.key : "";
  emit("update:modelValue", newValue);
};
</script>

<style scoped>
.box-card {
  margin-top: 10px;
  margin-bottom: 10px;
}
</style>
