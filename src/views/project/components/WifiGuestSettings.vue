<template>
  <el-card
    class="box-card"
    shadow="always"
    :style="{ marginTop: '10px', marginBottom: '10px' }"
    v-if="supports.includes('guest') && modelValue"
  >
    <el-header>
      <el-form-item :label="$t('device.guestWifi')" label-position="left" label-width="160px">
        <el-switch
          :model-value="modelValue.disabled"
          @update:model-value="updateField('disabled', $event)"
          active-color="#13ce66"
          :active-value="0"
          :inactive-value="1"
          inactive-color="#ff4949"
          :active-text="$t('device.open')"
          :inactive-text="$t('device.close')"
        />
      </el-form-item>
    </el-header>
    <el-main v-if="modelValue.disabled === 0">
      <el-form-item :label="$t('device.name')" label-position="left" label-width="160px" prop="ssid">
        <el-input :model-value="modelValue.ssid" @update:model-value="updateField('ssid', $event)" clearable />
      </el-form-item>
      <el-form-item :label="$t('device.encryptionMethod')" label-position="left" label-width="160px">
        <el-select :model-value="encryptionMethod" @update:model-value="updateEncryptionMethod">
          <el-option :label="$t('device.encryption')" :value="true" />
          <el-option :label="$t('device.encryptionNone')" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('device.key')" label-position="left" label-width="160px" v-if="encryptionMethod" prop="key">
        <el-input
          type="password"
          :model-value="modelValue.key"
          @update:model-value="updateField('key', $event)"
          show-password
          clearable
          @clear="$emit('passwordClear', 'guest')"
        />
      </el-form-item>
      <el-form-item :label="$t('device.rate')" label-position="left" label-width="160px" prop="rate">
        <el-select
          :model-value="modelValue.rate"
          @update:model-value="updateField('rate', $event)"
          allow-create
          filterable
          :placeholder="$t('device.wirelessRateTip')"
          @change="$emit('guestRateChange')"
        >
          <el-option v-for="item in guestRateOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('device.wifiTime')" label-position="left" label-width="160px">
        <el-select :model-value="modelValue.wifiTime" @update:model-value="updateField('wifiTime', $event)">
          <el-option :label="$t('device.forever')" :value="0" />
          <el-option :label="`${2} ${$t('device.hour')}`" :value="2" />
          <el-option :label="`${4} ${$t('device.hour')}`" :value="4" />
          <el-option :label="`${8} ${$t('device.hour')}`" :value="8" />
          <el-option :label="`${12} ${$t('device.hour')}`" :value="12" />
          <el-option :label="`${24} ${$t('device.hour')}`" :value="24" />
        </el-select>
      </el-form-item>
    </el-main>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from "vue";
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  supports: {
    type: Array,
    default: () => []
  },
  guestRateOptions: {
    type: Array,
    default: () => []
  }
});
const emit = defineEmits(["update:modelValue", "passwordClear", "guestRateChange"]);

const updateField = (field: string, value: any) => {
  const newValue = { ...props.modelValue };
  newValue[field] = value;
  emit("update:modelValue", newValue);
};

const encryptionMethod = computed(() => props.modelValue.key !== "");

const updateEncryptionMethod = (value: boolean) => {
  const newValue = { ...props.modelValue };
  newValue.key = value ? newValue.key : "";
  emit("update:modelValue", newValue);
};
</script>

<style scoped>
.box-card {
  margin-top: 10px;
  margin-bottom: 10px;
}
</style>
