import { useWifiSettings } from "./useWifiSettings";
import { useDeviceBasic } from "./useDeviceBasic";
import { usePortSettings } from "./usePortSettings";
import { useChartData } from "./useChartData";
import { useFormValidation } from "./useFormValidation";
import { useTabManagement } from "./useTabManagement";
// 设备配置相关的所有 composable 函数
export { useDeviceBasic } from "./useDeviceBasic";
export { useWifiSettings } from "./useWifiSettings";
export { usePortSettings } from "./usePortSettings";
export { useChartData } from "./useChartData";
export { useFormValidation } from "./useFormValidation";
export { useTabManagement } from "./useTabManagement";

/**
 * 设备配置抽屉的主要 composable 函数
 * 整合所有子模块的功能
 */
export function useDeviceConfigDrawer() {
  const deviceBasic = useDeviceBasic();
  const wifiSettings = useWifiSettings();
  const portSettings = usePortSettings();
  const chartData = useChartData();
  const formValidation = useFormValidation();
  const tabManagement = useTabManagement();

  return {
    // 设备基础信息
    ...deviceBasic,

    // WiFi设置
    ...wifiSettings,

    // 端口设置
    ...portSettings,

    // 图表数据
    ...chartData,

    // 表单验证
    ...formValidation,

    // 标签页管理
    ...tabManagement
  };
}
