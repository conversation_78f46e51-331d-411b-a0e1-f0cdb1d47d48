<template>
  <div class="app-wrapper glass-bg">
    <!-- 装饰元素 -->
    <div
      class="decoration-circle"
      style="
        top: -50px;
        right: -50px;
        width: 300px;
        height: 300px;
        background: radial-gradient(circle, rgb(255 255 255 / 80%) 0%, rgb(255 255 255 / 0%) 70%);
        opacity: 0.4;
      "
    ></div>
    <div
      class="decoration-circle"
      style="
        bottom: -100px;
        left: -100px;
        width: 400px;
        height: 400px;
        background: radial-gradient(circle, rgb(173 216 230 / 80%) 0%, rgb(173 216 230 / 0%) 70%);
        opacity: 0.3;
      "
    ></div>

    <el-config-provider :locale="locale" :size="assemblySize" :button="buttonConfig">
      <router-view></router-view>
    </el-config-provider>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, computed } from "vue";
import { useI18n } from "vue-i18n";
import { getBrowserLang, resetLanguageSettings } from "@/utils";
import { useTheme } from "@/hooks/useTheme";
import { ElConfigProvider } from "element-plus";
import { LanguageType } from "./stores/interface";
import { useGlobalStore } from "@/stores/modules/global";
import en from "element-plus/es/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { initLanguageWatcher } from "@/routers/index";

const globalStore = useGlobalStore();

// init theme
const { initTheme } = useTheme();
initTheme();

// init language
const i18n = useI18n();
onMounted(() => {
  // 在开发环境下，可以强制重新检测浏览器语言（可选）
  const isDev = import.meta.env.DEV;
  const urlParams = new URLSearchParams(window.location.search);
  const forceDetect = urlParams.has("force-lang-detect"); // 通过 ?force-lang-detect 强制检测

  // 开发环境下暴露工具函数到全局
  if (isDev) {
    (window as any).resetLanguageSettings = resetLanguageSettings;
    console.log("🛠️ 开发模式：可在控制台使用 resetLanguageSettings() 重置语言设置");
  }

  let language: string;

  if (forceDetect || (isDev && !globalStore.language)) {
    // 强制检测浏览器语言
    language = getBrowserLang();
    console.log("🌐 强制检测浏览器语言:", language);
  } else {
    // 使用保存的语言设置或检测浏览器语言
    language = globalStore.language ?? getBrowserLang();
  }

  i18n.locale.value = language;
  globalStore.setGlobalState("language", language as LanguageType);

  // 初始化语言变化监听器，确保切换语言时浏览器标题也更新
  initLanguageWatcher();
});

// element language
const locale = computed(() => {
  if (globalStore.language == "zh") return zhCn;
  if (globalStore.language == "en") return en;
  return getBrowserLang() == "zh" ? zhCn : en;
});

// element assemblySize
const assemblySize = computed(() => globalStore.assemblySize);

// element button config
const buttonConfig = reactive({ autoInsertSpace: false });
</script>

<style scoped>
.app-wrapper {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.decoration-circle {
  position: absolute;
  z-index: 0;
  border-radius: 50%;
}

/* 确保内容在装饰元素之上 */
:deep(.app-wrapper > *) {
  position: relative;
  z-index: 1;
}
</style>
