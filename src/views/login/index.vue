<template>
  <div class="login-container flx-center">
    <div class="login-box">
      <div class="login-actions">
        <SwitchDark class="dark-switch" />
        <SwitchLanguage class="language-switch" />
      </div>
      <div class="login-left">
        <img class="login-left-img" src="@/assets/images/login_left6.png" alt="login" />
      </div>
      <div class="login-form login-card">
        <div class="login-logo">
          <img class="login-icon" src="@/assets/images/logo.png" alt="" />
          <h2 class="logo-text">
            <div ref="chart" class="chart-container"></div>
          </h2>
        </div>
        <LoginForm />
        <div class="login-footer">
          <div class="app-download-section">
            <span class="download-text">{{ $t("common.downloadApp") }}</span>
            <AppQrCode class="app-qr-code" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="beian-wrapper">
    <div class="footer-main-row">
      <div class="beian-info">
        <a class="beian-link" href="https://beian.miit.gov.cn" target="_blank" :title="$t('footer.beianLink')">
          {{ $t("footer.beianNumber") }}
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="login">
import LoginForm from "./components/LoginForm.vue";
import SwitchDark from "@/components/SwitchDark/index.vue";
import SwitchLanguage from "@/components/SwitchLanguage/index.vue";
import AppQrCode from "@/components/AppQrCode/index.vue";
import * as d3 from "d3";
import { onMounted, ref, nextTick } from "vue";

const chart = ref<HTMLDivElement | null>(null);

onMounted(async () => {
  await nextTick();
  setTimeout(() => {
    if (chart.value) {
      // 清空容器
      chart.value.innerHTML = "";
      const width = 280,
        height = 80;
      const svg = d3.select(chart.value).append("svg").attr("width", width).attr("height", height);

      const text = svg
        .append("text")
        .attr("x", width / 2)
        .attr("y", height / 2 + 16)
        .attr("text-anchor", "middle")
        .attr("font-size", 48)
        .attr("font-weight", "bold")
        .attr("stroke", "#6DA9E8")
        .attr("stroke-width", 2)
        .attr("fill", "transparent")
        .text("CloudIot");

      const textNode = text.node() as SVGTextElement;
      const textLength = textNode.getComputedTextLength();
      text.attr("stroke-dasharray", textLength).attr("stroke-dashoffset", textLength);

      function animate() {
        text.attr("fill", "transparent").attr("stroke-dashoffset", textLength);
        text
          .transition()
          .duration(2000)
          .attr("stroke-dashoffset", 0)
          .on("end", () => {
            text
              .transition()
              .duration(800)
              .attr("fill", "#387DDE")
              .on("end", () => {
                setTimeout(animate, 1200); // 循环动画
              });
          });
      }
      animate();
    }
  }, 100);
});
</script>

<style scoped lang="scss">
@import "./index";
.login-actions {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  gap: 12px;
  align-items: center;
}
.login-box {
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 96.5%;
  height: 94%;
  padding: 0 50px;
  margin-bottom: 18px;
  background-color: rgb(255 255 255 / 30%);
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 18px;
  box-shadow: 0 8px 32px rgb(0 0 0 / 10%);
  transition:
    box-shadow 0.3s,
    border-radius 0.3s;
}
.login-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-width: 320px;
  max-width: 400px;
  padding: 38px 32px 32px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgb(64 158 255 / 10%);
  transition:
    box-shadow 0.3s,
    border-radius 0.3s;
  .login-logo {
    margin-bottom: 32px;
    .chart-container {
      width: 280px;
      height: 80px;
      margin: 10px auto 0;
    }
  }
  .login-footer {
    width: 100%;
    padding-top: 20px;
    margin-top: 24px;
    border-top: 1px solid #f0f0f0;
    .app-download-section {
      display: flex;
      gap: 12px;
      align-items: center;
      justify-content: center;
      .download-text {
        font-size: 13px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }
  }
}

// 移动端优化
@media screen and (width <=768px) {
  .login-container {
    padding: 20px 12px;
  }
  .login-box {
    box-sizing: border-box;
    flex-direction: column;
    width: 100%;
    min-width: 0;
    height: auto;
    padding: 20px;
    margin-bottom: 15px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgb(64 158 255 / 12%);
  }
  .login-left {
    display: none; // 隐藏左侧装饰图片
  }
  .login-card {
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    max-width: none;
    padding: 24px 20px 20px;
    margin: 0;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgb(64 158 255 / 10%);
    .login-logo {
      margin-bottom: 20px;
      .login-icon {
        width: 48px;
        height: 48px;
      }
      .chart-container {
        display: none; // 移动端隐藏动画
      }
    }
  }
}

@media screen and (width <=480px) {
  .login-container {
    padding: 16px 8px;
  }
  .login-box {
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 14px;
  }
  .login-card {
    box-sizing: border-box;
    padding: 20px 16px 16px;
    margin: 0;
    border-radius: 14px;
    box-shadow: 0 1px 8px rgb(64 158 255 / 8%);
    .login-logo {
      margin-bottom: 16px;
      .login-icon {
        width: 42px;
        height: 42px;
      }
      .chart-container {
        display: none; // 移动端隐藏动画
      }
    }
  }
}

@media screen and (width <=375px) {
  .login-container {
    padding: 12px 6px;
  }
  .login-box {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 12px;
  }
  .login-card {
    box-sizing: border-box;
    padding: 16px 12px 12px;
    margin: 0;
    border-radius: 12px;
    .login-logo {
      margin-bottom: 12px;
      .login-icon {
        width: 38px;
        height: 38px;
      }
      .chart-container {
        display: none; // 移动端隐藏动画
      }
    }
  }
}
.beian-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  width: 100vw;
  padding: 6px 12px 3px;
  background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, rgb(255 255 255 / 70%) 50%, rgb(255 255 255 / 85%) 100%);
  backdrop-filter: blur(8px);
  border-top: 1px solid rgb(255 255 255 / 15%);
  transition: all 0.3s ease;

  // 黑暗模式适配
  :global(.dark) & {
    background: linear-gradient(180deg, rgb(0 0 0 / 0%) 0%, rgb(0 0 0 / 60%) 50%, rgb(0 0 0 / 75%) 100%);
    border-top: 1px solid rgb(255 255 255 / 10%);
  }
  .footer-main-row {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    font-size: 11px;
    .beian-info {
      display: flex;
      align-items: center;
      justify-content: center;
      .beian-link {
        display: inline-flex;
        align-items: center;
        padding: 1px 6px;
        font-size: 10px;
        color: #999999;
        text-decoration: none;
        letter-spacing: 0.1px;
        background: transparent;
        border-radius: 6px;
        opacity: 0.6;
        transition: all 0.2s ease;

        // 黑暗模式适配
        :global(.dark) & {
          color: #666666;
          opacity: 0.5;
        }
        &:hover {
          color: #4b73c9;
          background: rgb(64 158 255 / 5%);
          opacity: 0.8;

          // 黑暗模式下的hover效果
          :global(.dark) & {
            color: #8bb9ff;
            background: rgb(139 185 255 / 8%);
            opacity: 0.7;
          }
        }
      }
    }
  }

  @media screen and (width <=480px) {
    padding: 2px 4px 1px;
    .footer-main-row {
      font-size: 9px;
      .beian-link {
        padding: 1px 4px;
        font-size: 9px;
        border-radius: 4px;
      }
    }
  }
}
</style>
