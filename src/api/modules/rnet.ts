import http from "@/api";
import { PORT1 } from "@/api/config/servicePort";
import { reactive, nextTick } from "vue";
import internetIcon from "@/assets/images/internet_icon.png";

import bridgeIcon_main from "@/assets/images/bridge_icon.png";
import switch_icon from "@/assets/images/switch_icon.png";
import router_icon from "@/assets/images/router_icon.png";
import ac_icon from "@/assets/images/ac_icon.png";
import ap_icon from "@/assets/images/ap_icon.png";
import repeater_icon from "@/assets/images/zhongji_icon.png";
import { ECOption } from "@/components/ECharts/config";
import * as echarts from "echarts";
import { labelFormatter, getRnetDeviceStatusJwe, updateLinesEffect, getLineStyle, convertBytes } from "@/api/interface/rnet";
import i18n from "@/languages/index";
const t = i18n.global.t;

export const getRnetList = () => {
  return http.post(PORT1 + `/rnet/queryRnet`);
};

export const createRnet = (params: { rnetName: string }) => {
  return http.post(PORT1 + `/rnet/createRnet`, params, { cancel: false });
};

export const editRnet = (params: { id: string | number; rnetName: string }) => {
  return http.post(PORT1 + `/rnet/updateRnet`, params, { cancel: false });
};

export const deleteRnet = (params: { id: string | number }) => {
  return http.post(PORT1 + `/rnet/deleteRnet`, params, { cancel: false });
};

export const queryRnetDevice = (params: { id: string }, config: any = {}) => {
  console.log(PORT1 + `/rnet/queryRnetDevice`, params.id);
  return http.get(PORT1 + `/rnet/queryRnetDevice`, params, { cancel: false, ...config });
};

// Add interface for device response data
interface DeviceResponseData {
  deviceName?: string;
  deviceType?: string;
  status?: number;
  supports?: string[];
  deviceId: string;
}

interface DeviceResponse {
  code: string;
  data: DeviceResponseData;
}

// Update getDeviceById to include type
export const getDeviceById = (params: { deviceId: string }): Promise<DeviceResponse> => {
  return http.get(PORT1 + `/device/getDeviceById`, params, {
    cancel: false
  });
};

export const data = reactive({
  name: "internet",
  symbol: "image://" + internetIcon,
  children: []
});

// const loadImageAsBase64 = async (url: string): Promise<string> => {
//   const response = await fetch(url);
//   const blob = await response.blob();
//   return new Promise(resolve => {
//     const reader = new FileReader();
//     reader.onload = () => resolve(reader.result as string);
//     reader.readAsDataURL(blob);
//   });
// };

export const getDeviceIcon = (deviceType: string) => {
  console.log("deviceType:---", deviceType);
  if (deviceType === "route") {
    return "image://" + router_icon;
  } else if (deviceType === "switch") {
    return "image://" + switch_icon;
  } else if (deviceType === "ac") {
    return "image://" + ac_icon;
  } else if (deviceType === "ap") {
    return "image://" + ap_icon;
  } else if (deviceType === "bridge") {
    return "image://" + bridgeIcon_main;
  } else if (deviceType === "repeater") {
    return "image://" + repeater_icon;
  }
  return "";
};

export const getDeviceImg = (deviceType: string) => {
  console.log("deviceType:---", deviceType);
  if (deviceType === "route") {
    return router_icon;
  } else if (deviceType === "switch") {
    return switch_icon;
  } else if (deviceType === "ac") {
    return ac_icon;
  } else if (deviceType === "ap") {
    return ap_icon;
  } else if (deviceType === "bridge") {
    return bridgeIcon_main;
  } else if (deviceType === "repeater") {
    return repeater_icon;
  }
  return ""; // 默认返回空字符串，或者可以设置一个默认图片路径
};

export const getDeviceList = () => {
  return http.get(PORT1 + `/device/queryDevices`, {}, { cancel: false });
};

export const saveRnetDevice = (params: { rnetId: string; peer: any[] }) => {
  return http.post(PORT1 + `/rnet/addRnetDevice`, params, { cancel: true });
};

export const deleteRnetDevice = (params: { rnetId: string; peer: any[] }) => {
  return http.post(PORT1 + `/rnet/deleteRnetDevice`, params, {
    cancel: true
  });
};

// 新增：将设备保存到用户分组
export const saveDeviceGroup = (params: { groupId: string; deviceId: string }) => {
  return http.post(PORT1 + `/group/saveDeviceGroup`, params);
};

export interface Device {
  deviceId: string;
  status: number;
  supports: string[];
  rnetId: string;
  deviceName: string;
}
export const rnetForm = reactive({
  rnetName: ""
});

export const form = reactive({
  userId: "",
  deviceId: "",
  data: {
    system: {
      rNet: {
        custom: 0,
        allowedIPs: [""], // 初始时有一个空字符串
        rnetId: ""
      }
    }
  }
});

// 设置 rich 样式 - 导入全局状态以获取当前主题
import { useGlobalStore } from "@/stores/modules/global";

// 创建一个计算属性，根据当前主题返回适当的rich样式
export const getRichStyle = () => {
  const globalStore = useGlobalStore();
  const isDark = globalStore.isDark;

  return {
    a: {
      fontSize: 14,
      color: isDark ? "#ffffff" : "#000000", // 明亮模式下使用绝对黑色增强可读性
      fontWeight: "bold", // 始终使用粗体增强可读性
      textShadow: isDark ? "0 0 2px #000000" : "0 0 2px #ffffff" // 添加文字阴影增强可读性
    },
    b: {
      fontSize: 12,
      color: isDark ? "#ffffff" : "#333333", // 明亮模式下使用深色增强可读性
      fontWeight: "bold", // 始终使用粗体增强可读性
      textShadow: isDark ? "0 0 2px #000000" : "0 0 2px #ffffff" // 添加文字阴影增强可读性
    }
  };
};

// 导出rich对象以保持兼容性
export const rich = getRichStyle();

export const option: ECOption = {
  animation: true,
  animationDurationUpdate: 2000,
  animationEasingUpdate: "quinticInOut",
  title: {
    text: t("common.remoteNetworkTopology"),
    textStyle: {
      color: useGlobalStore().isDark ? "#ffffff" : "#333333",
      fontWeight: "bold"
    }
  },
  // 固定坐标系为图表容器尺寸，初始值会被 updateAxisRange 函数更新
  xAxis: { show: false, min: 0, max: 1000 },
  yAxis: { show: false, min: 0, max: 800 },
  // 根据当前主题设置背景色
  backgroundColor: useGlobalStore().isDark ? "rgba(30, 32, 40, 0.8)" : "transparent",
  series: [
    {
      type: "graph",
      layout: "circular", // 或 "force"
      // circular: {
      //   rotateLabel: true // 保证标签旋转
      // },
      force: { repulsion: 100 },
      symbolSize: 30,
      roam: false,
      label: {
        show: true,
        position: "bottom",
        formatter: labelFormatter,
        rich: rich
      },
      edgeSymbol: ["none", "none"],
      edgeSymbolSize: [0, 10],
      edgeLabel: { fontSize: 8 },
      data: [], // 节点数据由 getRnetDevice 更新
      links: [], // 连线数据由 getRnetDevice 更新（包括 direction 属性）
      lineStyle: {
        opacity: 0.9,
        width: 2,
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: "#00C6FF" },
            { offset: 1, color: "#00FF88" }
          ]
        },
        // 设置弧度（正值表示向外弧线，负值表示反向弧线）
        curveness: 0,
        emphasis: { focus: "adjacency", edgeSymbolSize: [0, 20] }
      },
      effect: {
        show: true,
        period: 4,
        trailLength: 0.2,
        symbol: "circle",
        symbolSize: 3,
        loop: true
      }
    },
    {
      // 用于动态流动效果的小圆点系列
      id: "linesSeries",
      type: "lines",
      coordinateSystem: "cartesian2d",
      polyline: true,
      zlevel: 1,
      silent: true, // 禁用交互，避免干扰主图表
      animation: true,
      animationDuration: 1000,
      animationEasing: "linear",
      effect: {
        show: true,
        period: 4,
        trailLength: 0.1,
        symbol: "circle",
        symbolSize: 5,
        loop: true
      },
      // 设置与 graph 连接线相同的曲率
      lineStyle: {
        color: "#ff7f0e",
        width: 0,
        curveness: 0,
        opacity: 0.6
      },
      data: []
    }
  ]
};

// 获取远程网络设备
export const getRnetDevice = async (id: string, devices: any[], option: any, chartInstance: any) => {
  if (!id) return;
  try {
    const res = await queryRnetDevice({ id });
    if (res.code !== "200" || !Array.isArray(res.data)) {
      console.error("Failed to fetch valid data");
      return;
    }
    console.log("res.data:---", JSON.stringify(res.data));
    devices.length = 0; // 清空数组但保持引用
    let nodes: any[] = [];
    let links: any[] = [];
    const deviceNameMap = new Map();
    const deviceSymbolMap = new Map();
    const deviceResMap = new Map();

    for (const item of res.data) {
      const deviceRes = await getDeviceById({ deviceId: item.deviceId });
      // 获取设备名称
      const getDeviceName = () => {
        if (!deviceRes.data) {
          return "";
        }
        return deviceRes.data.deviceName || "";
      };
      const name = getDeviceName();
      deviceNameMap.set(item.deviceId, name);
      deviceResMap.set(item.deviceId, deviceRes);
    }

    // 遍历每个设备请求，不用 Promise.all 集中等待
    res.data.forEach(item => {
      (async () => {
        try {
          const deviceRes = deviceResMap.get(item.deviceId);

          if (deviceRes && deviceRes.code === "200" && deviceRes.data) {
            const device = deviceRes.data;
            console.log("deviceRes.dat:---", JSON.stringify(deviceRes.data));
            // 检查设备是否已存在
            if (!devices.some(d => d.deviceId === device.deviceId)) {
              devices.push(device);
              devices.sort((a, b) => a.status - b.status);
            }
            const name = device.deviceName || device.deviceId;
            const icon = getDeviceIcon(device.deviceType);
            let rateStr = "0 B/s";
            const rnetConfig = await getRnetDeviceStatusJwe({ deviceId: item.deviceId });
            if (rnetConfig?.code === "200" && rnetConfig.data?.system?.rNet) {
              const sys = rnetConfig.data.system.rNet;
              const peers = sys.peer || [];
              if (peers.length > 0) {
                peers.forEach((p: any) => {
                  if (!globalPeers[p.deviceId]) {
                    globalPeers[p.deviceId] = { rxByte: 0, txByte: 0 };
                  }
                  globalPeers[p.deviceId].rxByte += Number(p.rxByte) || 0;
                  globalPeers[p.deviceId].txByte += Number(p.txByte) || 0;
                });
              }
            }
            nodes.push({ name, symbol: icon, rate: rateStr, deviceId: item.deviceId });
            deviceSymbolMap.set(item.deviceId, icon);

            // 构造连线数据（根据已有数据增量更新）
            if (rnetConfig?.code === "200" && rnetConfig.data?.system?.rNet?.peer) {
              const peers = rnetConfig.data.system.rNet.peer;
              // 如果需要更新设备数据（例如：device.data = rnetConfig.data）
              devices.forEach(device => {
                if (device.deviceId === item.deviceId) {
                  device.data = rnetConfig.data;
                }
              });
              peers.forEach((peer: any) => {
                if (item.deviceId === peer.deviceId) return;
                const sourceName = deviceNameMap.get(item.deviceId) || item.deviceId;
                const targetName = deviceNameMap.get(peer.deviceId) || peer.deviceId;
                const [s, t] = [sourceName, targetName].sort();
                const linkKey = `${s}-${t}`;
                // 避免重复连线
                if (!links.some(link => link.linkKey === linkKey)) {
                  links.push({
                    source: sourceName,
                    target: targetName,
                    direction: "normal",
                    sourceDeviceId: item.deviceId,
                    targetDeviceId: peer.deviceId,
                    lineStyle: getLineStyle(peer.status),
                    linkKey // 用于标识去重
                  });
                }
              });
            }

            // 每个请求完成后立即更新图表数据
            option.series[0].data = nodes;

            // 输出连线数据以便调试
            console.log(`Updating chart with ${nodes.length} nodes and ${links.length} links`);
            links.forEach((link, index) => {
              console.log(`Link ${index}: ${link.source} -> ${link.target} (${link.direction || "normal"})`);
            });

            // 处理连线数据，移除 linkKey 属性
            option.series[0].links = links.map(link => {
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              const { linkKey, ...rest } = link;
              return rest;
            });

            await nextTick();
            if (chartInstance) {
              try {
                // 防御性检查图表实例是否有效
                try {
                  const dom = chartInstance.getDom();
                  if (!dom) {
                    console.warn("Chart DOM element not found in getRnetDevice");
                    return;
                  }
                } catch (e) {
                  console.warn("Chart instance has been disposed in getRnetDevice");
                  return;
                }

                // 使用 nextTick 确保不在主进程中调用 setOption
                await nextTick();

                // 更新图表数据
                console.log("Setting chart options with updated data");
                chartInstance.setOption(option, { notMerge: false, lazyUpdate: true, silent: true });
                console.log("Chart options updated in getRnetDevice");

                // 使用 setTimeout 避免在主进程中调用 dispatchAction
                setTimeout(() => {
                  try {
                    chartInstance?.dispatchAction({ type: "hideTip" });
                    setTimeout(() => {
                      chartInstance?.dispatchAction({ type: "showTip" });
                    }, 100);
                  } catch (error) {
                    console.error("Error in dispatchAction:", error);
                  }
                }, 0);

                // 等待一些时间再触发 updateLinesEffect 函数，确保图表已经完成渲染
                setTimeout(async () => {
                  try {
                    console.log("Updating lines effect after device data update...");
                    await updateLinesEffect(chartInstance, option);
                    console.log("Lines effect updated successfully after device data update");
                  } catch (error) {
                    console.error("Error updating lines effect after device data update:", error);
                  }
                }, 100); // 等待 100ms 再触发，确保图表已经完成渲染
              } catch (error) {
                console.error("Error updating chart in getRnetDevice:", error);
              }
            }
          }
        } catch (error) {
          console.error(`Error fetching device ${item.deviceId}:`, error);
        }
      })();
    });
  } catch (error) {
    console.error("Error in getRnetDevice:", error);
  }
};

// 轮询更新：每 5 秒更新所有设备的 rnetConfig，并根据汇总的 peer 数据更新节点速率和连线方向
export const pollRnetConfigs = async (
  chartInstance: any,
  option: any,
  devices: any[],
  globalPeers: any,
  updateChartIfValid: Function
) => {
  try {
    console.log("Polling for data...");

    // 获取设备数据
    const dataFetched = await fetchDeviceData(devices, globalPeers);
    if (!dataFetched) {
      console.log("No data fetched in polling");
      return;
    }

    console.log("Data fetched successfully in polling");

    // 检查图表实例是否存在且有效
    let chartValid = false;
    if (chartInstance) {
      try {
        const dom = chartInstance.getDom();
        if (dom) {
          chartValid = true;
        }
      } catch (e) {
        console.log("Chart instance check failed in pollRnetConfigs");
      }
    }

    // 如果图表实例有效，触发图表更新事件
    if (chartValid) {
      updateChartIfValid(chartInstance, option, devices, globalPeers);
    } else {
      console.log("Chart instance not valid, skipping chart update");
    }
  } catch (error) {
    console.error("Error in pollRnetConfigs:", error);
  }
};

// 获取设备数据的函数，不依赖图表实例
export const fetchDeviceData = async (devices: any[], globalPeers: any) => {
  try {
    // 存储所有响应信息
    const resultsForPeers: any[] = [];

    // 防御性地遍历设备
    if (!devices || !Array.isArray(devices)) {
      console.warn("Invalid devices data in fetchDeviceData");
      return false;
    }

    // 获取设备数据
    for (const item of devices) {
      try {
        if (!item || !item.deviceId) continue;

        const rnetConfig = await getRnetDeviceStatusJwe({ deviceId: item.deviceId });
        if (rnetConfig && rnetConfig.code === "200") {
          resultsForPeers.push(rnetConfig);
        }
      } catch (error) {
        console.error(`Error fetching rnet config for device ${item?.deviceId}:`, error);
      }
    }

    // 如果没有获取到数据，返回 false
    if (!resultsForPeers.length) {
      return false;
    }

    // 更新全局数据
    updateGlobalPeers(resultsForPeers, devices, globalPeers);

    return true;
  } catch (error) {
    console.error("Error in fetchDeviceData:", error);
    return false;
  }
};

// 更新全局数据的函数
export const updateGlobalPeers = (resultsForPeers: any[], devices: any[], globalPeers: any) => {
  try {
    const newPeers = {};
    resultsForPeers.forEach(resItem => {
      if (resItem && resItem.data && resItem.data.system.rNet.peer) {
        resItem.data.system.rNet.peer.forEach((p: any) => {
          if (!newPeers[p.deviceId]) {
            newPeers[p.deviceId] = { rxByte: 0, txByte: 0 };
          }
          newPeers[p.deviceId].rxByte = Number(p.rxByte) || 0;
          newPeers[p.deviceId].txByte = Number(p.txByte) || 0;
          newPeers[p.deviceId].time = Number(p.time) || 0;
          newPeers[p.deviceId].status = String(p.status) || "0";
        });
      }
    });

    // 更新设备速率
    devices.forEach(device => {
      const rateData = newPeers[device.deviceId];
      const oldPeer = globalPeers[device.deviceId];
      let rateStr = "0 B/s";
      if (rateData) {
        // 计算速率差值
        const rxDiff = rateData.rxByte - (oldPeer?.rxByte || 0);
        const txDiff = rateData.txByte - (oldPeer?.txByte || 0);
        const timeDiff = rateData.time - (oldPeer?.time || 0);

        // 确保时间差不为0,并且流量差值为正数
        if (timeDiff > 0) {
          const total = Math.max(0, (rxDiff + txDiff) / timeDiff);
          if (isNumber(total)) {
            rateStr = convertBytes(total) + "/s";
          }
        }
      }
      device.rate = rateStr;
    });

    // 将新数据保存到全局数据
    resultsForPeers.forEach(resItem => {
      if (resItem && resItem.data && resItem.data.system.rNet.peer) {
        resItem.data.system.rNet.peer.forEach((p: any) => {
          if (!globalPeers[p.deviceId]) {
            globalPeers[p.deviceId] = { rxByte: 0, txByte: 0 };
          }
          globalPeers[p.deviceId].rxByte = Number(p.rxByte) || 0;
          globalPeers[p.deviceId].txByte = Number(p.txByte) || 0;
          globalPeers[p.deviceId].time = Number(p.time) || 0;
        });
      }
    });

    return true;
  } catch (error) {
    console.error("Error in updateGlobalPeers:", error);
    return false;
  }
};

// 初始化或重新初始化图表的函数，用于初始化和主题切换
export const reinitializeChart = async (
  isDarkMode: boolean,
  chartRef: any,
  option: any,
  chartInstance: any,
  stopPolling: Function,
  startPolling: Function,
  updateChartIfValid: Function
) => {
  try {
    console.log("Initializing/Reinitializing chart with theme:", isDarkMode ? "dark" : "light");

    // 先停止现有轮询
    stopPolling();

    // 如果存在旧实例，销毁它
    let instanceDisposed = false;
    if (chartInstance) {
      try {
        // 防御性检查图表实例是否有效
        try {
          const dom = chartInstance.getDom();
          if (!dom) {
            console.warn("Chart DOM element not found, instance may already be disposed");
            instanceDisposed = true;
          }
        } catch (domError) {
          console.warn("Error checking chart DOM, instance may already be disposed:", domError);
          instanceDisposed = true;
        }

        // 只有当实例有效时才尝试销毁
        if (!instanceDisposed) {
          chartInstance.dispose();
          console.log("Old chart instance disposed");
        }
      } catch (e) {
        console.warn("Error disposing chart instance:", e);
      }
      chartInstance = null; // 确保实例被清空
    }

    // 等待DOM更新
    await nextTick();

    // 不再依赖 chartRef，直接使用 DOM 选择器获取图表容器

    // 创建新实例
    console.log("Creating chart instance with chartRef");
    try {
      // 直接使用 ID 选择器获取 DOM 元素
      let domElement: Element = document.getElementById("rnet-chart");

      if (!domElement) {
        console.error("Could not find #rnet-chart element for chart initialization");
        // 尝试其他方式获取元素
        const fallbackElement = document.querySelector(".content-box") || document.querySelector(".echarts-container");
        if (!fallbackElement) {
          console.error("Could not find any suitable element for chart initialization");
          startPolling();
          return false;
        }
        console.log("Using fallback element for chart initialization:", fallbackElement);
        domElement = fallbackElement;
      } else {
        console.log("Using #rnet-chart element for chart initialization");
      }

      console.log("DOM element dimensions:", domElement.clientWidth, "x", domElement.clientHeight);

      // 创建新实例，使用当前主题
      chartInstance = echarts.init(domElement as HTMLElement, isDarkMode ? "dark" : undefined, { renderer: "canvas" });
      console.log("New chart instance created with size:", domElement.clientWidth, "x", domElement.clientHeight);

      // 设置选项
      await chartInstance.setOption(option, { notMerge: true, silent: true });
      console.log("Chart options set successfully");

      // 重新注册事件
      chartInstance.on("rendered", async () => {
        try {
          await nextTick();
          await updateLinesEffect(chartInstance, option);
        } catch (error) {
          console.error("Error in rendered event handler:", error);
        }
      });

      console.log("Chart successfully initialized with theme:", isDarkMode ? "dark" : "light");

      // 启动轮询
      startPolling();

      // 立即尝试更新图表，使用当前数据
      updateChartIfValid(chartInstance, option, [], {});

      return true;
    } catch (error) {
      console.error("Error creating new chart instance:", error);

      // 不再尝试使用 chartRef.getInstance 方法，因为在某些组件中 chartRef 只是 DOM 引用
      console.log("Skipping chartRef.getInstance fallback method");

      // 即使创建失败，仍然启动轮询以获取数据
      startPolling();
      return false;
    }
  } catch (error) {
    console.error("Error in reinitializeChart:", error);
    // 即使出错，仍然启动轮询以获取数据
    startPolling();
    return false;
  }
};

// 全局变量
export const globalPeers = reactive({});

// 判断是否为数字
export const isNumber = (val: unknown): val is number => {
  return typeof val === "number" && !isNaN(val);
};
