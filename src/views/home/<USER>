<template>
  <div class="main-box glass-container">
    <div class="main-content">
      <el-row :gutter="20" class="home-row">
        <!-- 左侧图表区域 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="chart-col">
          <el-card class="box-card glass-card">
            <template #header>
              <div class="clearfix">
                <span>{{ t("common.deviceStatistics") }}</span>
                <div class="header-actions">
                  <el-button type="primary" class="action-button refresh-button" :icon="Refresh" @click="refreshData">
                    {{ t("common.refresh") }}
                  </el-button>
                </div>
              </div>
            </template>
            <D3BarChart
              :y-axis-data="yAxisData"
              :online="online"
              :offline="offline"
              :is-dark="globalStore.isDark"
              :legend-online="t('common.onLine')"
              :legend-offline="t('common.offLine')"
            />
          </el-card>
        </el-col>
        <!-- 右侧消息区域 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="message-col">
          <el-card class="box-card glass-card">
            <template #header>
              <div class="clearfix">
                <span>{{ t("common.messageCenter") }}</span>
                <el-button type="primary" class="action-button refresh-button" :icon="Refresh" @click="refreshMessages">
                  {{ t("common.refresh") }}
                </el-button>
              </div>
            </template>
            <div class="message-container">
              <el-empty v-if="!unreadMessage?.length" :description="t('common.noData')" />
              <el-timeline v-else>
                <el-timeline-item
                  v-for="(message, index) in unreadMessage"
                  :key="index"
                  :timestamp="message.creationTime"
                  type="primary"
                >
                  <el-card class="message-card glass-content-card">
                    <div class="message-header">
                      <h4>{{ isChinese ? message.title : message.titleEn || message.title }}</h4>
                      <span class="message-time">{{ formatTime(message.creationTime) }}</span>
                    </div>
                    <div class="message-content">
                      <p>{{ isChinese ? message.message : message.messageEn || message.message }}</p>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 悬浮APP下载按钮 -->
    <div class="floating-app-download">
      <AppQrCode class="floating-qr-code" />
    </div>
  </div>
</template>

<script setup lang="ts" name="home">
import { computed, onMounted, ref, watch } from "vue";
import { getUserDeviceStatistics } from "@/api/modules/home";
import { useI18n } from "vue-i18n";
import { getUnreadMessages } from "@/api/modules/message";
import { useGlobalStore } from "@/stores/modules/global";
import { getDeviceType } from "@/api/modules/project";
import { Refresh } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import AppQrCode from "@/components/AppQrCode/index.vue";
import D3BarChart from "@/components/D3BarChart/index.vue";

const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh");
const { t } = useI18n();

const yAxisData = ref<string[]>([]);
const online = ref<number[]>([]);
const offline = ref<number[]>([]);
const unreadMessage = ref<any>(null); // 改为 any/null
const deviceType = ref<any>(null); // 改为 any/null
const originalData = ref<any>(null); // 改为 any/null

// 数据处理逻辑，赋值给 D3BarChart
async function updateChartData(data?: any) {
  const userDeviceStatistics = data || (await getUserDeviceStatistics());
  if (userDeviceStatistics && userDeviceStatistics.code == "200" && Array.isArray(userDeviceStatistics.data)) {
    originalData.value = userDeviceStatistics;

    const originalYAxisData = [
      ...new Set(
        userDeviceStatistics.data.map((item: any) => {
          return item.deviceType;
        })
      )
    ];

    let convertedYAxisData = [...originalYAxisData];
    if (isChinese.value) {
      convertedYAxisData = originalYAxisData.map(item => {
        if (deviceType.value) {
          const deviceTypeItem = deviceType.value.find((deviceTypeItem: any) => deviceTypeItem.configCode === item);
          return deviceTypeItem ? deviceTypeItem.configDesc : item;
        }
        return item;
      });
    } else {
      convertedYAxisData = originalYAxisData.map(item => {
        if (deviceType.value) {
          const deviceTypeItem = deviceType.value.find((deviceTypeItem: any) => deviceTypeItem.configCode === item);
          return deviceTypeItem ? deviceTypeItem.attribute2 : item;
        }
        return item;
      });
    }

    yAxisData.value = convertedYAxisData as string[];
    online.value = [];
    offline.value = [];

    originalYAxisData.forEach(type => {
      const onlineItem = userDeviceStatistics.data.find((item: any) => item.deviceType === type && item.status === 0);
      const offlineItem = userDeviceStatistics.data.find((item: any) => item.deviceType === type && item.status === 1);

      online.value.push(onlineItem ? onlineItem.deviceCount : 0);
      offline.value.push(offlineItem ? offlineItem.deviceCount : 0);
    });
  }
}

// 刷新数据
const refreshData = async () => {
  await updateChartData();
  ElMessage.success(t("common.refreshSuccess"));
};

// 刷新消息
const refreshMessages = async () => {
  const result = await getUnreadMessages();
  if (result && result.code === "200") {
    unreadMessage.value = result.data;
  } else {
    unreadMessage.value = [];
  }
  ElMessage.success(t("common.refreshSuccess"));
};

// 格式化时间
const formatTime = (time: string) => {
  return time;
};

onMounted(async () => {
  const result = await getUnreadMessages();
  if (result && result.code === "200") {
    unreadMessage.value = result.data;
  } else {
    unreadMessage.value = [];
  }
  const typeResult = await getDeviceType();
  if (typeResult && typeResult.code === "200") {
    deviceType.value = typeResult.data;
  } else {
    deviceType.value = [];
  }
  await updateChartData();
});

// 监听语言变化
watch(isChinese, () => {
  if (originalData.value) {
    updateChartData(originalData.value);
  }
});

// 监听暗黑模式变化
watch(
  () => globalStore.isDark,
  () => {
    if (originalData.value) {
      setTimeout(() => {
        updateChartData(originalData.value);
      }, 300);
    }
  }
);
</script>

<style scoped lang="scss">
@import "./index";
</style>
