<template>
  <div>
    <div v-if="portSettingsLoading" style="padding: 16px">
      <el-skeleton :rows="8" animated />
    </div>
    <div v-else>
      <el-divider v-if="deviceConfig.system.swPort && drawerProps.row?.deviceType === 'switch'"></el-divider>
      <el-card
        v-if="drawerProps.row?.deviceType === 'switch' && deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0"
      >
        <div class="sw-port-container">
          <!-- 端口数 <= 5 时，单行显示 -->
          <ul v-if="deviceConfig.system.swPort.length <= 5" class="sw-port-list single-row">
            <li
              v-for="(item, index) in deviceConfig.system.swPort"
              :key="index"
              :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
              @click="toggleRowSelection(item)"
            >
              <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
              <div class="port-name-container">
                <el-input
                  v-if="item.isEditing"
                  v-model="item.describe"
                  size="small"
                  ref="portNameInput"
                  @blur="handleDescribeConfirm(item)"
                  @keyup.enter="handleDescribeConfirm(item)"
                  @keyup.esc="handleDescribeCancel(item)"
                  maxlength="32"
                  show-word-limit
                  class="port-name-input"
                ></el-input>
                <div v-else class="port-name-display" @click.stop="handleDescribeEdit(item)">
                  <el-text class="port-name-text" :title="item.describe || item.name">{{ item.describe || item.name }}</el-text>
                  <el-icon class="edit-icon"><Edit /></el-icon>
                </div>
              </div>
            </li>
          </ul>

          <!-- 端口数 > 5 时，两行显示：GE1 GE3 GE5 / GE2 GE4 GE6 -->
          <div v-else class="sw-port-multi-row">
            <!-- 第一行：奇数索引端口 -->
            <ul class="sw-port-row">
              <li
                v-for="(item, index) in deviceConfig.system.swPort.filter((_, i) => i % 2 === 0)"
                :key="'odd-' + index"
                :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
                @click="toggleRowSelection(item)"
              >
                <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                <div class="port-name-container">
                  <el-input
                    v-if="item.isEditing"
                    v-model="item.describe"
                    size="small"
                    ref="portNameInput"
                    @blur="handleDescribeConfirm(item)"
                    @keyup.enter="handleDescribeConfirm(item)"
                    @keyup.esc="handleDescribeCancel(item)"
                    maxlength="32"
                    show-word-limit
                    class="port-name-input"
                  ></el-input>
                  <div v-else class="port-name-display" @click.stop="handleDescribeEdit(item)">
                    <el-text class="port-name-text" :title="item.describe || item.name">{{ item.describe || item.name }}</el-text>
                    <el-icon class="edit-icon"><Edit /></el-icon>
                  </div>
                </div>
              </li>
            </ul>

            <!-- 第二行：偶数索引端口 -->
            <ul class="sw-port-row">
              <li
                v-for="(item, index) in deviceConfig.system.swPort.filter((_, i) => i % 2 === 1)"
                :key="'even-' + index"
                :class="['sw-port-tag', { 'selected-port': isRowSelected(item) }]"
                @click="toggleRowSelection(item)"
              >
                <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                <div class="port-name-container">
                  <el-input
                    v-if="item.isEditing"
                    v-model="item.describe"
                    size="small"
                    ref="portNameInput"
                    @blur="handleDescribeConfirm(item)"
                    @keyup.enter="handleDescribeConfirm(item)"
                    @keyup.esc="handleDescribeCancel(item)"
                    maxlength="32"
                    show-word-limit
                    class="port-name-input"
                  ></el-input>
                  <div v-else class="port-name-display" @click.stop="handleDescribeEdit(item)">
                    <el-text class="port-name-text" :title="item.describe || item.name">{{ item.describe || item.name }}</el-text>
                    <el-icon class="edit-icon"><Edit /></el-icon>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <el-button link :icon="showPortExample ? 'ArrowUp' : 'ArrowDown'" @click="togglePortExample">{{
          showPortExample ? t("common.CollapseDiagram") : t("common.ExpandDiagram")
        }}</el-button>
        <ul class="sw-port-list" v-if="showPortExample">
          <li v-for="(item, index) in portStates" :key="index" class="sw-port-example">
            <el-image :src="item.icon" class="port-icon" :alt="item.text" />
            <span class="port-text">{{ item.text }}</span>
          </li>
        </ul>
      </el-card>
      <el-divider></el-divider>
      <el-button size="default" type="primary" @click="openPortDialog">{{ t("device.configuration") }}</el-button>
      <el-table
        ref="portTableRef"
        :data="deviceConfig.system.swPort"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :selection="selectedRows"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column :label="$t('device.portName')">
          <template #default="scope">
            <el-input
              v-if="scope.row.isEditing"
              v-model="scope.row.describe"
              ref="tablePortNameInput"
              @blur="handleDescribeConfirm(scope.row)"
              @keyup.enter="handleDescribeConfirm(scope.row)"
              @keyup.esc="handleDescribeCancel(scope.row)"
              maxlength="32"
              show-word-limit
            >
            </el-input>
            <div v-else class="edit-cell" @click="handleDescribeEdit(scope.row)">
              {{ scope.row.describe || scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="portenable" :label="t('device.portStatus')">
          <template #default="scope">
            <span>{{ scope.row.portenable === 1 ? $t("device.open") : $t("device.close") }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('device.portPowerLimit')" width="180px">
          <template #default="scope">
            <span>{{ deviceConfig.system?.swPoe[scope.$index]?.power ?? "--" }}/</span>
            {{
              deviceConfig.system.swPoe[scope.$index]?.powerout === 0
                ? "af (15.4w)"
                : deviceConfig.system.swPoe[scope.$index]?.powerout === 1
                  ? "at (30w)"
                  : "--"
            }}
          </template>
        </el-table-column>
        <el-table-column prop="power" :label="$t('device.portRate')">
          <template #default="scope">
            {{ scope.row.link === 1 ? formatSpeedDuplex(scope.row.speed_duplex) : formatAutoneg(scope.row.autoneg) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog v-model="portDialogVisible" :title="$t('device.portSettings')" width="700px" draggable>
      <el-text style="margin: 10px; font-size: 16px; font-weight: bolder; text-align: center">
        {{ t("device.configApplyTip") }}
      </el-text>
      {{ selectedRowNames }}
      <el-card v-if="deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0">
        <el-tabs tab-position="left" style="height: 300px" class="demo-tabs" v-model="dialogTabActive">
          <el-tab-pane :label="$t('device.portSettings')" name="first">
            <el-form ref="portForm" :model="swPort" label-width="160px" :hide-required-asterisk="true">
              <el-form-item :label="$t('device.portSwitch')">
                <el-switch v-model="swPort.portenable" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.portExtend')">
                <el-switch v-model="swPort.extend" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.adaptive')" v-if="swPort.extend === 0">
                <el-switch v-model="swPort.autoneg" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.flowControl')" v-if="swPort.autoneg == 0 && swPort.extend === 0">
                <el-switch v-model="swPort.flwctrl" :inactive-value="0" :active-value="1" />
              </el-form-item>
              <el-form-item :label="$t('device.speedDuplex')" v-if="swPort.autoneg == 0 && swPort.extend === 0">
                <el-select v-model="swPort.speed_duplex" :placeholder="t('common.pleaseSelect')">
                  <el-option
                    v-for="speedOption in speedDuplexOptions"
                    :key="speedOption.value"
                    :label="formatSpeedDuplex(speedOption.label)"
                    :value="speedOption.value"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <!-- POE配置 -->
          <el-tab-pane :label="$t('device.PoeSettings')" name="second">
            <el-form ref="poeForm" :model="swPoe" label-width="100px" :hide-required-asterisk="true">
              <el-form-item :label="$t('device.poeSwitch')">
                <el-switch v-model="swPoe.poeenable" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.poeClass')">
                <el-text>{{ swPoe.poeclass }}</el-text>
              </el-form-item>
              <el-form-item :label="$t('device.portPowerLimit')">
                <el-select v-model="swPoe.powerout" :placeholder="t('common.pleaseSelect')">
                  <el-option v-for="(label, value) in poePowerOptions" :key="value" :label="label" :value="Number(value)" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('device.portPower')">
                <el-text>{{ (swPoe.power || 0) + " W" }}</el-text>
              </el-form-item>
              <el-form-item :label="$t('device.poeWatchDog')">
                <el-switch v-model="swPoe.poewd" :active-value="1" :inactive-value="0" />
              </el-form-item>
              <el-form-item :label="$t('device.PoeWatchDogTime')" v-if="swPoe.poewd === 1">
                <el-input-number v-model="swPoe.poetime" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('device.VlanSettings')" name="third">
            <el-form ref="vlanForm" :model="swVlan" :rules="vlanRules" label-width="100px" :hide-required-asterisk="true">
              <el-form-item :label="$t('device.portVlan')" prop="vlanmode">
                <el-select v-model="swVlan.vlanmode" :placeholder="t('common.pleaseSelect')">
                  <el-option v-for="(label, value) in vlanModeOptions" :key="value" :label="label" :value="Number(value)" />
                </el-select>
              </el-form-item>
              <el-form-item label="VLAN" prop="pvid">
                <el-input-number v-model="swVlan.pvid" :placeholder="t('device.pvidPlaceholder')" :min="1" :max="4094" />
              </el-form-item>
              <el-form-item label="Permit VLAN" prop="permit" v-if="swVlan.vlanmode == 1 || swVlan.vlanmode == 2">
                <el-input v-model="permitVlanString" @input="updatePermitVlan" :placeholder="t('device.pvidPlaceholder')" />
              </el-form-item>
              <el-form-item label="Untag VLAN" prop="untag" v-if="swVlan.vlanmode == 2">
                <el-input v-model="untagVlanString" @input="updateUntagVlan" :placeholder="t('device.pvidPlaceholder')" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <!-- 风暴抑制 -->
          <el-tab-pane :label="$t('device.StormSetting')" name="fourth">
            <el-form ref="stormForm" :model="swStorm" label-width="100px" :hide-required-asterisk="true">
              <el-form-item :label="$t('device.trafficType')">
                <el-checkbox-group v-model="selectedTrafficTypes" @change="updateTrafficType">
                  <el-checkbox
                    v-for="trafficOption in trafficTypeOptionsArray"
                    :key="trafficOption.value"
                    :value="trafficOption.value"
                  >
                    {{ trafficOption.description }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item :label="t('common.unicast')" v-if="selectedTrafficTypes.includes(4)">
                <el-tree-select
                  v-model="swStorm.rate3"
                  :data="rateOptions"
                  :props="{
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    checkStrictly: true,
                    disabled: data =>
                      data.value === 'common' ||
                      data.value === 'custom' ||
                      (typeof data.value === 'string' && data.value.startsWith('range-')),
                    isLeaf: data => !data.children || data.children.length === 0,
                    hasChildren: data =>
                      data.value === 'custom' || (typeof data.value === 'string' && data.value.startsWith('range-'))
                  }"
                  check-strictly
                  node-key="value"
                  :expand-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-form-item>

              <el-form-item :label="t('common.multicast')" v-if="selectedTrafficTypes.includes(1)">
                <el-tree-select
                  v-model="swStorm.rate1"
                  :data="rateOptions"
                  :props="{
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    checkStrictly: true,
                    disabled: data =>
                      data.value === 'common' ||
                      data.value === 'custom' ||
                      (typeof data.value === 'string' && data.value.startsWith('range-')),
                    isLeaf: data => !data.children || data.children.length === 0,
                    hasChildren: data =>
                      data.value === 'custom' || (typeof data.value === 'string' && data.value.startsWith('range-'))
                  }"
                  check-strictly
                  node-key="value"
                  :expand-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-form-item>

              <el-form-item :label="t('common.broadcast')" v-if="selectedTrafficTypes.includes(2)">
                <el-tree-select
                  v-model="swStorm.rate2"
                  :data="rateOptions"
                  :props="{
                    label: 'label',
                    value: 'value',
                    children: 'children',
                    checkStrictly: true,
                    disabled: data =>
                      data.value === 'common' ||
                      data.value === 'custom' ||
                      (typeof data.value === 'string' && data.value.startsWith('range-')),
                    isLeaf: data => !data.children || data.children.length === 0,
                    hasChildren: data =>
                      data.value === 'custom' || (typeof data.value === 'string' && data.value.startsWith('range-'))
                  }"
                  check-strictly
                  node-key="value"
                  :expand-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('device.IsolationControl')" name="fifth">
            <el-form ref="isolationForm" :model="swPoe" label-width="100px" :hide-required-asterisk="true">
              <el-card v-if="deviceConfig.system.swPort && deviceConfig.system.swPort.length > 0">
                <div class="sw-port-container">
                  <!-- 端口数 <= 5 时，单行显示 -->
                  <ul v-if="deviceConfig.system.swPort.length <= 5" class="sw-port-list single-row">
                    <li
                      v-for="item in deviceConfig.system.swPort"
                      :key="item.port"
                      :class="['sw-port-tag', { 'selected-port': isPortSelected(item) }]"
                      @click="togglePortSelection(item)"
                    >
                      <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                      <div class="port-name-container">
                        <div class="port-name-display">
                          <el-text class="port-name-text" :title="item.name">{{ item.name }}</el-text>
                        </div>
                      </div>
                    </li>
                  </ul>

                  <!-- 端口数 > 5 时，两行显示 -->
                  <div v-else class="sw-port-multi-row">
                    <!-- 第一行：奇数索引端口 -->
                    <ul class="sw-port-row">
                      <li
                        v-for="item in deviceConfig.system.swPort.filter((_, i) => i % 2 === 0)"
                        :key="'odd-' + item.port"
                        :class="['sw-port-tag', { 'selected-port': isPortSelected(item) }]"
                        @click="togglePortSelection(item)"
                      >
                        <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                        <div class="port-name-container">
                          <div class="port-name-display">
                            <el-text class="port-name-text" :title="item.name">{{ item.name }}</el-text>
                          </div>
                        </div>
                      </li>
                    </ul>

                    <!-- 第二行：偶数索引端口 -->
                    <ul class="sw-port-row">
                      <li
                        v-for="item in deviceConfig.system.swPort.filter((_, i) => i % 2 === 1)"
                        :key="'even-' + item.port"
                        :class="['sw-port-tag', { 'selected-port': isPortSelected(item) }]"
                        @click="togglePortSelection(item)"
                      >
                        <el-image :src="item.link ? portActiveIcon : portDeactiveIcon" class="port-icon-img" alt="Port Icon" />
                        <div class="port-name-container">
                          <div class="port-name-display">
                            <el-text class="port-name-text" :title="item.name">{{ item.name }}</el-text>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
                <el-button link @click="isolateAll">{{ t("common.selectAll") }}</el-button>
                <el-divider></el-divider>
                <el-text>
                  {{ t("common.selectedPorts") }}:
                  <!-- 如果 isolateRows 为空，显示 swIsolate，否则显示 isolateRows 的内容 -->
                  <span v-if="isolateRows.length === 0">
                    <!-- 根据 swIsolate 数组的数字，匹配 deviceConfig.system.swPort 的 port 字段，显示对应的 name 字段 -->
                    {{ getPortNames(swIsolate).join(", ") }}
                  </span>
                  <span v-else>
                    {{ isolateRows.map(item => item.name).join(", ") }}
                  </span>
                </el-text>
              </el-card>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('device.QosSettings')" name="sixth">
            <el-form ref="qosForm" :model="swQos" label-width="100px" :hide-required-asterisk="true">
              <el-form-item :label="$t('device.qosTip')">
                <el-select v-model="swQos.qos" :placeholder="t('common.pleaseSelect')">
                  <!-- 动态生成 0-7 的选项 -->
                  <el-option v-for="value in 8" :key="value" :label="`QoS ${value - 1}`" :value="value - 1" />
                </el-select>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-card>
      <template #footer>
        <el-button @click="portDialogVisible = false">{{ t("common.cancel") }}</el-button>
        <el-button type="primary" :disabled="!hasDialogConfigChanged" @click="() => handleSubmit(true)">{{
          t("common.confirm")
        }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="PortSettings">
import { computed, ref, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";

import { Edit } from "@element-plus/icons-vue";
import {
  formatAutoneg,
  getPortNames,
  handleSelectionChange,
  isRowSelected,
  poePowerOptions,
  portDialogVisible,
  portTableRef,
  rateOptions,
  selectedRowNames,
  selectedRows,
  selectedTrafficTypes,
  showPortExample,
  speedDuplexOptions,
  swIsolate,
  swPoe,
  swPort,
  swQos,
  swStorm,
  swVlan,
  togglePortExample,
  toggleRowSelection,
  trafficTypeOptionsArray,
  updateTrafficType,
  vlanModeOptions
} from "@/api/interface/deviceConfigDrawer";
import { formatSpeedDuplex } from "@/api/interface/device/formatter";
import portActiveIcon from "@/assets/images/port_active_icon.png";
import portDeactiveIcon from "@/assets/images/port_deactive_icon.png";
import { isolateAll, isolateRows, isPortSelected, togglePortSelection } from "@/api/interface/apGroupDrawer";

const { t } = useI18n();

// 添加一个变量来强制刷新树
const treeKey = ref(0);

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  drawerProps: {
    type: Object,
    required: true
  }
});

const deviceConfig = computed(() => props.modelValue);

const portSettingsLoading = ref(false);
const portStates = ref([]);
const dialogTabActive = ref("first");
const permitVlanString = ref("");
const untagVlanString = ref("");

const openPortDialog = () => {
  portDialogVisible.value = true;
};

const handleDescribeEdit = (row: any) => {
  console.log("开始编辑端口描述:", row);
  // 保存原始值，用于取消时恢复
  row.originalDescribe = row.describe;
  row.isEditing = true;

  // 使用 nextTick 确保输入框渲染后再聚焦
  nextTick(() => {
    const inputs = document.querySelectorAll('input[maxlength="32"]');
    const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
    if (lastInput) {
      lastInput.focus();
      lastInput.select();
    }
  });
};

const handleDescribeCancel = (row: any) => {
  console.log("取消端口描述编辑:", row);
  // 恢复原始值
  if (row.originalDescribe !== undefined) {
    row.describe = row.originalDescribe;
    delete row.originalDescribe;
  }
  row.isEditing = false;
};

const handleDescribeConfirm = async (row: any) => {
  console.log("确认端口描述修改:", row);
  row.isEditing = false;

  try {
    await modifyDescribe(row);
    console.log("端口描述修改成功");
    // 清理原始值
    delete row.originalDescribe;
  } catch (error) {
    console.error("端口描述修改失败:", error);
    ElMessage.error("端口描述修改失败");
    // 如果修改失败，恢复原始值
    if (row.originalDescribe !== undefined) {
      row.describe = row.originalDescribe;
      delete row.originalDescribe;
    }
  }
};

const modifyDescribe = async (row: any) => {
  console.log("修改描述:", stringifyWithoutEmpty(row));

  // 验证描述长度（最大32个字节）
  if (row.describe && new Blob([row.describe]).size > 32) {
    ElMessage.error("端口描述最大长度为32个字节");
    throw new Error("端口描述长度超限");
  }

  // 构造提交数据，只包含必要的字段
  const portData = {
    name: row.name,
    port: row.port,
    describe: row.describe || row.name, // 如果描述为空，使用端口名称
    portenable: row.portenable,
    autoneg: row.autoneg,
    extend: row.extend,
    speed_duplex: row.speed_duplex,
    flwctrl: row.flwctrl
  };

  console.log("提交端口描述数据:", portData);
  await submitConfig({ system: { swPort: [portData] } });
};

const updatePermitVlan = (value: string) => {
  swVlan.value.permit = value
    .split(",")
    .map(s => parseInt(s.trim(), 10))
    .filter(n => !isNaN(n));
};

const updateUntagVlan = (value: string) => {
  swVlan.value.untag = value
    .split(",")
    .map(s => parseInt(s.trim(), 10))
    .filter(n => !isNaN(n));
};

const handleNodeClick = data => {
  console.log("点击的节点:", data);

  // 如果是"更多选择..."节点
  if (data.value === "custom") {
    console.log("点击了更多选择...节点");

    // 如果节点没有子节点或者子节点为空数组，加载子节点
    if (!data.children || data.children.length === 0) {
      // 生成速率范围选项
      const ranges = [
        { min: 64, max: 1024, step: 64 },
        { min: 1024, max: 10240, step: 512 },
        { min: 10240, max: 102400, step: 2048 },
        { min: 102400, max: 1024000, step: 10240 }
      ];

      const rangeOptions = ranges.map(range => ({
        label: `${range.min} - ${range.max}`,
        value: `range-${range.min}-${range.max}`,
        children: [], // 使用空数组而不是null，确保显示展开图标
        isLeaf: false,
        hasChildren: true,
        range
      }));

      // 更新节点的子节点
      data.children = rangeOptions;

      // 强制刷新树
      treeKey.value++;
    }
  }

  // 如果是范围节点
  if (data.value && typeof data.value === "string" && data.value.startsWith("range-")) {
    console.log("点击了范围节点:", data.value);

    // 如果节点没有子节点或者子节点为空数组，加载子节点
    if (!data.children || data.children.length === 0) {
      // 从range-min-max格式中提取min和max
      const parts = data.value.split("-");
      if (parts.length === 3) {
        const min = parseInt(parts[1], 10);
        const max = parseInt(parts[2], 10);

        // 根据范围决定步长
        let step = 64;
        if (min >= 1024 && min < 10240) {
          step = 512;
        } else if (min >= 10240 && min < 102400) {
          step = 2048;
        } else if (min >= 102400) {
          step = 10240;
        }

        // 生成范围内的所有速率选项
        const values = [];
        for (let value = min; value <= max; value += step) {
          values.push({
            label: `${value}`,
            value: value,
            isLeaf: true
          });
        }

        // 更新节点的子节点
        data.children = values;

        // 强制刷新树
        treeKey.value++;
      }
    }
  }
};

const vlanRules = computed(() => {
  return {
    vlanmode: [
      {
        required: true,
        message: t("device.portVlan") + t("common.pleaseSelect"),
        trigger: "change"
      }
    ],
    pvid: [
      {
        required: true,
        message: "VLAN ID" + t("common.pleaseInput"),
        trigger: "blur"
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value !== null && value !== undefined) {
            const num = Number(value);
            if (isNaN(num) || !Number.isInteger(num) || num < 1 || num > 4094) {
              callback(new Error("VLAN ID必须是1-4094之间的整数"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    permit: [
      {
        validator: (rule: any, value: any, callback: any) => {
          // 只在trunk或hybrid模式下验证
          if (swVlan.value.vlanmode === 1 || swVlan.value.vlanmode === 2) {
            if (!permitVlanString.value || permitVlanString.value.trim() === "") {
              callback(new Error("Permit VLAN" + t("common.pleaseInput")));
            } else {
              // 验证VLAN ID格式
              const vlanIds = permitVlanString.value.split(",").map(id => id.trim());
              for (const id of vlanIds) {
                const num = parseInt(id, 10);
                if (isNaN(num) || num < 1 || num > 4094) {
                  callback(new Error("Permit VLAN ID必须是1-4094之间的整数，多个ID用逗号分隔"));
                  return;
                }
              }
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    untag: [
      {
        validator: (rule: any, value: any, callback: any) => {
          // 只在hybrid模式下验证
          if (swVlan.value.vlanmode === 2) {
            if (untagVlanString.value && untagVlanString.value.trim() !== "") {
              // 验证VLAN ID格式
              const vlanIds = untagVlanString.value.split(",").map(id => id.trim());
              for (const id of vlanIds) {
                const num = parseInt(id, 10);
                if (isNaN(num) || num < 1 || num > 4094) {
                  callback(new Error("Untag VLAN ID必须是1-4094之间的整数，多个ID用逗号分隔"));
                  return;
                }
              }
            }
            callback();
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  };
});
</script>

<style scoped lang="scss">
// Placeholder for style
</style>
